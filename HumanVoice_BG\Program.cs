﻿using HumanVoice_Backstage.Communication;
using HumanVoice_Backstage.LLM;
using HumanVoice_Backstage.TTS;
using NAudio.Lame;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace HumanVoice_Backstage
{
    public class AccessToken
    {
        private static string EncodeText(string text)
        {
            return Uri.EscapeDataString(text)
                      .Replace("+", "%20")
                      .Replace("*", "%2A")
                      .Replace("%7E", "~");
        }

        private static string EncodeDict(Dictionary<string, string> dict)
        {
            var sortedDict = dict.OrderBy(kvp => kvp.Key, StringComparer.Ordinal)
                                 .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            var queryString = string.Join("&", sortedDict.Select(kvp => $"{EncodeText(kvp.Key)}={EncodeText(kvp.Value)}"));
            return queryString;
        }

        public static (string Token, long ExpireTime) CreateToken(string accessKeyId, string accessKeySecret)
        {
            var parameters = new Dictionary<string, string>
            {
                { "AccessKeyId", accessKeyId },
                { "Action", "CreateToken" },
                { "Format", "JSON" },
                { "RegionId", "cn-shanghai" },
                { "SignatureMethod", "HMAC-SHA1" },
                { "SignatureNonce", Guid.NewGuid().ToString() },
                { "SignatureVersion", "1.0" },
                { "Timestamp", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ") },
                { "Version", "2019-02-28" }
            };

            // 构造规范化的请求字符串
            var queryString = EncodeDict(parameters);
            Console.WriteLine($"规范化的请求字符串: {queryString}");

            // 构造待签名字符串
            var stringToSign = "GET" + "&" + EncodeText("/") + "&" + EncodeText(queryString);
            Console.WriteLine($"待签名的字符串: {stringToSign}");

            // 计算签名
            using (var hmac = new HMACSHA1(Encoding.UTF8.GetBytes(accessKeySecret + "&")))
            {
                var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign));
                var signature = Convert.ToBase64String(hash);
                Console.WriteLine($"签名: {signature}");

                // 进行URL编码
                var encodedSignature = EncodeText(signature);
                Console.WriteLine($"URL编码后的签名: {encodedSignature}");

                // 调用服务
                var fullUrl = $"http://nls-meta.cn-shanghai.aliyuncs.com/?Signature={encodedSignature}&{queryString}";
                Console.WriteLine($"url: {fullUrl}");

                // 提交HTTP GET请求
                using (var client = new HttpClient())
                {
                    var response = client.GetAsync(fullUrl).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var content = response.Content.ReadAsStringAsync().Result;
                        var rootObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(content);
                        if (rootObj["Token"] != null)
                        {
                            var token = rootObj["Token"]["Id"].ToString();
                            var expireTime = (long)rootObj["Token"]["ExpireTime"];
                            return (token, expireTime);
                        }
                    }
                    Console.WriteLine(response.Content.ReadAsStringAsync().Result);
                }
            }

            return (null, 0);
        }
    }



    internal class Program
    {
        static HttpListener httpListener;


        public class Rootobject
        {
            public string input { get; set; }
            public string response_format { get; set; }
            public bool stream { get; set; }
            public int speed { get; set; }
            public int gain { get; set; }

            public extra_body extra_Body;
        }

        public sealed class extra_body
        {
            public references references { get; set; }
        }
        public class references
        {
            public string audio;
            public string text;
        }

        static byte[] ConvertWavToWavAsync(byte[] mp3Data, int sampleRate = 16000)
        {
            using var ffmpegProcess = new Process();
            ffmpegProcess.StartInfo.FileName = "ffmpeg";
            ffmpegProcess.StartInfo.Arguments = $"-hide_banner -loglevel error -f wav -i pipe:0 -f wav -ar {sampleRate} pipe:1";
            ffmpegProcess.StartInfo.UseShellExecute = false;
            ffmpegProcess.StartInfo.RedirectStandardInput = true;  // 启用标准输入
            ffmpegProcess.StartInfo.RedirectStandardOutput = true; // 启用标准输出
            ffmpegProcess.StartInfo.RedirectStandardError = true;
            ffmpegProcess.StartInfo.CreateNoWindow = true;

            // 启动 FFmpeg
            ffmpegProcess.Start();

            // 同步写入 MP3 数据到标准输入
            ffmpegProcess.StandardInput.BaseStream.WriteAsync(mp3Data, 0, mp3Data.Length).Wait();
            ffmpegProcess.StandardInput.Close(); // 关闭输入流

            // 异步读取 WAV 输出
            using var ms = new MemoryStream();
            ffmpegProcess.StandardOutput.BaseStream.CopyTo(ms);

            ffmpegProcess.WaitForExit();

            return ms.ToArray();
        }




        static byte[] ConvertMp3ToWavAsync(byte[] mp3Data, int sampleRate = 16000, int chunkSize = 4096)
        {
            using var ffmpegProcess = new Process();
            ffmpegProcess.StartInfo.FileName = "ffmpeg";
            ffmpegProcess.StartInfo.Arguments = $"-hide_banner -loglevel error -f mp3 -i pipe:0 -f wav -ar {sampleRate} pipe:1";
            ffmpegProcess.StartInfo.UseShellExecute = false;
            ffmpegProcess.StartInfo.RedirectStandardInput = true;
            ffmpegProcess.StartInfo.RedirectStandardOutput = true;
            ffmpegProcess.StartInfo.RedirectStandardError = true;
            ffmpegProcess.StartInfo.CreateNoWindow = true;

            ffmpegProcess.Start();

            // **1. 先启动输出流读取（防止死锁）**
            var outputTask = Task.Run(() =>
            {
                using var ms = new MemoryStream();
                ffmpegProcess.StandardOutput.BaseStream.CopyTo(ms);
                return ms.ToArray();
            });

            ffmpegProcess.StandardInput.BaseStream.Write(
                     mp3Data,
                     0,
                     mp3Data.Length
                 );

            // **3. 关闭输入流（告诉 FFmpeg 数据已结束）**
            ffmpegProcess.StandardInput.Close();

            // **4. 等待 FFmpeg 处理完成**
            ffmpegProcess.WaitForExit();

            // **5. 检查是否有错误**
            string errors = ffmpegProcess.StandardError.ReadToEnd();
            if (!string.IsNullOrEmpty(errors))
            {
                throw new Exception($"FFmpeg Error: {errors}");
            }

            // **6. 返回 WAV 数据**
            return outputTask.Result;
        }

        static async Task Main(string[] args)
        {

            //// 获取Token
            //var (token, expireTime) = AccessToken.CreateToken("LTAI5tREaRXU2fZ1CHo4k1uv", "******************************");
            //if (token != null)
            //{
            //    Console.WriteLine($"Token: {token}");
            //    Console.WriteLine($"Expire Time (Unix Timestamp): {expireTime}");
            //    Console.WriteLine($"Token有效期的北京时间: {DateTimeOffset.FromUnixTimeSeconds(expireTime).ToLocalTime():yyyy-MM-dd HH:mm:ss}");
            //}
            //else
            //{
            //    Console.WriteLine("获取Token失败");
            //}
            //for (int i = 0; i < 3; i++)
            //{

            //    var sdg= Stopwatch.StartNew();
            //    HttpWebRequest httpWebRequest = HttpWebRequest.Create($"https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts?appkey=GXMy9LorFPgD02np&token={token}&text=你好呀，你是谁呀，我能帮到你什么&format=wav&sample_rate=16000&volume=100") as HttpWebRequest;
            //    var fs = File.Open(i+".wav", FileMode.OpenOrCreate);
            //    var s = httpWebRequest.GetResponse().GetResponseStream();
            //    s.CopyTo(fs);
            //    fs.Flush();
            //    fs.Close();

            //    sdg.Stop();
            //    Console.WriteLine(sdg.ElapsedMilliseconds);
            //}




            //TTS.TTS s = new TTS.TTS("BV700_streaming", TTSType.HuoShan);
            //var ff = s.Syn("你好呀，你是谁呀，我能帮到你什么");
            // Convert.ToBase64String(ff);



            //{"uri":"speech:xiaoniu:angz2th434:rmchxleewxithrslgjzg"}
            // for (int i = 0; i < 10; i++)
            //{
            //    var s = Stopwatch.StartNew();
            //    HttpWebRequest request = (HttpWebRequest)WebRequest.Create("https://api.siliconflow.cn/v1/audio/speech");
            //    request.Method = "POST";
            //    request.ContentType = "application/json";
            //    request.Headers["Authorization"] = $"Bearer sk-ftnkaxirnwgxpsckfupjcpbkpihzshedqmwlnaawmckgtuop";


            //    using (var streamWriter = new StreamWriter(request.GetRequestStream()))
            //    {

            //     //   streamWriter.Write("{\r\n  \"input\": \"你会做什么呀，我可以帮到你的吗\",\r\n  \"response_format\": \"wav\",\r\n  \"stream\": true,\r\n  \"speed\": 1,\r\n  \"gain\": 0,\r\n  \"voice\": \"FunAudioLLM/CosyVoice2-0.5B:alex\",\r\n  \"model\": \"FunAudioLLM/CosyVoice2-0.5B\",\r\n  \"sample_rate\": 16000\r\n}");
            //        streamWriter.Write("{\r\n  \"input\": \"欢迎你来我们学校参观呀，它允许用户轻松地将蓝牙功能集成设备\",\r\n  \"response_format\": \"wav\",\r\n  \"stream\": true,\r\n  \"speed\": 1,\r\n  \"gain\": 0,\r\n  \"voice\": \"speech:tanyingbaobao:angz2th434:cetadhlitvmwdefyktow\",\r\n  \"model\": \"FunAudioLLM/CosyVoice2-0.5B\",\r\n  \"sample_rate\": 44100\r\n}");
            //    }

            //    try
            //    {
            //        using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            //        using (var re = response.GetResponseStream())
            //        using (var fs = File.Open("sas.wav", FileMode.OpenOrCreate))
            //        {
            //            re.CopyTo(fs);
            //        }
            //    }
            //    catch (WebException ex)
            //    {
            //        try
            //        {
            //            using (var streamReader = new StreamReader(ex.Response.GetResponseStream()))
            //            {
            //                string errorResponse = streamReader.ReadToEnd();
            //                Console.WriteLine($"Error response: \n{errorResponse}");
            //            }
            //        }
            //        catch
            //        {

            //        }
            //    }
            //    s.Stop();

            //    await Task.Delay(1000);
            //    Console.WriteLine(s.ElapsedMilliseconds);
            //}
            //return;

            //foreach (string arg in Directory.GetFiles("Audios/male","*.wav"))
            //{
            //    TTS.TTS s = new TTS.TTS(Path.GetFileNameWithoutExtension(arg), TTSType.IndexTTSVLLM);
            //    var ffs = s.Generate("我知道这样很唐突，你不用现在回答我，真的!我可以等，等到你想清楚，等到你也愿意多看我一眼。就像这颗糖，甜得有点笨拙，但绝对是最纯粹的心意。");
            //    File.WriteAllBytes(Path.Combine( "C:\\Users\\<USER>\\Downloads\\khd" , Path.GetFileName(arg)), ffs);
            //    Console.WriteLine(Path.GetFileNameWithoutExtension(arg));
            //}
             

            TTS.TTS s=new TTS.TTS("病弱少女",type: TTSType.IndexTTSVLLM);
            s.Generate("aa");
            try
            {
                LameDLL.LoadNativeDLL(AppDomain.CurrentDomain.BaseDirectory);
            }
            catch
            {

            }
            new SocketCommunication().ReceiveData();

        }

    }
}
