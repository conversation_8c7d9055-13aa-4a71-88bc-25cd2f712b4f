# HumanVoice 人声后台服务项目

## 项目概述

HumanVoice_Backstage 是一个基于 C# .NET 8.0 开发的智能语音交互后台服务系统。该项目集成了语音识别（ASR）、大语言模型（LLM）和文本转语音（TTS）技术，提供完整的语音对话解决方案。

## 技术架构

### 核心技术栈
- **开发语言**: C# (.NET 8.0)
- **通信协议**: WebSocket
- **音频处理**: NAudio, FFmpeg
- **JSON处理**: Newtonsoft.Json
- **语音服务**: Microsoft Cognitive Services Speech
- **容器化**: Docker

### 主要依赖包
```xml
<PackageReference Include="Azure.AI.OpenAI" Version="2.0.0-beta.2" />
<PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.42.0" />
<PackageReference Include="NAudio" Version="2.2.1" />
<PackageReference Include="NAudio.Lame.CrossPlatform" Version="2.2.1" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

## 项目结构

```
HumanVoice_BG/
├── Program.cs                    # 主程序入口
├── Communication/                # 通信模块
│   ├── SocketCommunication.cs   # WebSocket服务器
│   └── DisponseData/            # 数据处理
│       ├── WebSocketClientDisponseData.cs  # 客户端数据处理
│       └── Struct/              # 数据结构定义
├── TTS/                         # 文本转语音模块
│   └── TTS.cs                   # TTS服务实现
├── LLM/                         # 大语言模型模块
│   ├── LLMBase.cs              # LLM基类
│   ├── LLM.cs                  # 标准LLM实现
│   ├── CozeLLM.cs              # Coze平台LLM
│   └── GuiJiVLM.cs             # 视觉语言模型
├── Config/                      # 配置文件
│   ├── HuoShanTTSConfig.json   # 火山引擎TTS配置
│   ├── DouBaoLLM.json          # 豆包LLM配置
│   └── GuiJi.json              # 硅基配置
├── Audios/                      # 音频文件存储
│   ├── female/                  # 女声音频样本
│   └── male/                    # 男声音频样本
└── Dockerfile                   # Docker配置
```

## 核心功能模块

### 1. 通信模块 (Communication)

**主要功能**:
- 基于 WebSocket 的实时通信服务
- 监听端口: 19465
- 支持多客户端并发连接
- 处理语音识别请求路径: `/recognition`

**关键特性**:
- 支持配置参数传递 (`isSendConfig`)
- 支持多种TTS类型选择 (`isKeLong`, `isFree`, `isLLMVoice`)
- 实时音频数据流处理
- 图像数据处理支持

### 2. TTS模块 (Text-to-Speech)

**支持的TTS服务**:

1. **HuoShan (火山引擎)** - 字节跳动TTS服务
   - 高质量语音合成
   - 支持多种声音类型
   - 16kHz WAV格式输出

2. **keLong (克隆)** - 火山引擎克隆版本
   - 语音克隆技术
   - 自定义音色支持

3. **Coze** - 扣子平台TTS
   - API密钥认证
   - 多种音色选择
   - 可调节语速

4. **Free** - 免费TTS服务
   - 有道词典TTS
   - 火山引擎免费版本
   - MP3/WAV格式支持

5. **IndexTTSVLLM** - 自定义TTS服务
   - 本地音频样本驱动
   - 支持男女声音库
   - 自定义服务器部署

**音频处理能力**:
- MP3 到 WAV 格式转换
- 采样率调整 (默认16kHz)
- 表情符号过滤
- 音频数据Base64编码

### 3. LLM模块 (Large Language Model)

**支持的LLM服务**:

1. **标准LLM** - 通用大语言模型
2. **CozeLLM** - 扣子平台智能体
3. **GuiJiVLM** - 硅基视觉语言模型
   - 支持图像理解
   - 多模态交互

**核心功能**:
- 流式响应处理
- 系统消息配置
- 回调机制支持
- 多轮对话管理

### 4. 语音识别集成

**FunASR集成**:
- WebSocket连接: `ws://117.72.11.94:10095/`
- 实时语音识别
- 2pass模式支持
- 热词配置
- 流式和离线识别

**识别配置**:
```json
{
  "chunk_size": [5,10,5],
  "wav_name": "h5",
  "is_speaking": true,
  "chunk_interval": 10,
  "itn": false,
  "mode": "2pass",
  "hotwords": "{\"你好\":20,\"小卿\":70,\"hello world\":40}"
}
```

### 5. 访问令牌管理

**阿里云NLS服务集成**:
- 自动Token生成
- HMAC-SHA1签名算法
- Token过期时间管理
- 自动刷新机制

## 工作流程

### 典型对话流程

1. **连接建立**
   - 客户端通过WebSocket连接到服务器
   - 发送配置信息（声音类型、系统消息等）

2. **语音输入**
   - 客户端发送音频数据
   - 服务器转发到FunASR进行语音识别

3. **文本处理**
   - 识别结果发送到LLM进行理解和生成
   - 支持图像+文本的多模态输入

4. **语音合成**
   - LLM响应文本发送到TTS服务
   - 生成对应的语音数据

5. **结果返回**
   - 将合成的语音数据返回给客户端
   - 支持流式响应

### 数据流转

```
音频输入 → FunASR识别 → LLM处理 → TTS合成 → 音频输出
    ↑                                           ↓
客户端 ←←←←←←←←← WebSocket通信 ←←←←←←←←←←←← 服务器
```

## 性能特性

### 并发处理
- 支持多客户端同时连接
- 使用ConcurrentDictionary管理客户端
- 异步任务队列处理
- 信号量控制并发数量

### 优化策略
- TTS预热机制
- 音频格式转换优化
- 内存池使用 (ArrayPool)
- 流式数据处理

### 监控统计
- TTS调用次数统计
- 免费服务使用计数
- 性能时间点记录
- 异常处理和日志

## 配置管理

### 主要配置文件

1. **HuoShanTTSConfig.json** - 火山引擎TTS配置
2. **DouBaoLLM.json** - 豆包LLM配置  
3. **GuiJi.json** - 硅基服务配置

### 环境变量
- API密钥管理
- 服务端点配置
- 功能开关控制

## 部署方案

### Docker部署
- 支持Linux容器
- 包含所有依赖
- 音频文件自动复制
- 配置文件映射

### 依赖服务
- FFmpeg (音频转换)
- FunASR服务器 (语音识别)
- 各TTS服务API
- LLM服务接口

## 扩展性设计

### 模块化架构
- 各功能模块独立
- 接口抽象设计
- 插件式TTS支持
- 可配置LLM后端

### 未来扩展方向
- 更多TTS服务集成
- 语音情感识别
- 多语言支持
- 语音克隆优化
- 实时语音转换

## 迁移到Python的考虑

### 技术对应关系
- **WebSocket**: `websockets` 或 `fastapi`
- **音频处理**: `pydub`, `librosa`
- **HTTP客户端**: `aiohttp`, `requests`
- **JSON处理**: 内置 `json` 模块
- **异步处理**: `asyncio`

### 主要挑战
1. **性能优化** - Python的GIL限制
2. **音频处理** - FFmpeg集成
3. **并发处理** - 异步编程模式
4. **依赖管理** - 第三方库兼容性
5. **部署复杂度** - 环境配置

### 建议的迁移策略
1. **模块化迁移** - 逐个模块移植
2. **接口保持** - 维持WebSocket协议兼容
3. **性能测试** - 对比C#版本性能
4. **渐进式替换** - 支持混合部署

---

*该文档基于对C#源代码的详细分析编写，为Python迁移提供全面的技术参考。*
