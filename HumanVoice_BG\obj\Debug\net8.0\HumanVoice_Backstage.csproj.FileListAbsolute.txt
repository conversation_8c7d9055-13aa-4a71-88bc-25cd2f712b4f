E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\sherpa-onnx.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\VS\HumanVoice_Backstage\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\model.onnx
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\tokens.txt
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Model\vad\silero_vad.onnx
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Azure.Core.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\OpenAI.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\System.ClientModel.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\System.Memory.Data.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\model.onnx
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\tokens.txt
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Model\vad\silero_vad.onnx
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Azure.Core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\OpenAI.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\sherpa-onnx.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\System.ClientModel.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\System.Memory.Data.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Debug\net8.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Model\vad\silero_vad.onnx
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\sherpa-onnx.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\libmp3lame.32.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\libmp3lame.64.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Model\en_2024_03_09\model.onnx
E:\Unity\Human_Back\HumanVoice_BG\bin\Debug\net8.0\Model\en_2024_03_09\tokens.txt
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Model\en_2024_03_09\model.onnx
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Model\en_2024_03_09\tokens.txt
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Model\vad\silero_vad.onnx
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\sherpa-onnx.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\星空一航_大模型对话\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.Asio.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.Midi.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.Wasapi.dll
E:\unity\星空一航_大模型对话\HumanVoice_BG\bin\Debug\net8.0\NAudio.WinMM.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Model\en_2024_03_09\model.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Model\en_2024_03_09\tokens.txt
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Model\vad\silero_vad.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.Asio.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.Midi.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.Wasapi.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\NAudio.WinMM.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\sherpa-onnx.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.Asio.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.Midi.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.Wasapi.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\NAudio.WinMM.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
E:\VS\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
E:\VS\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
E:\VS\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.Asio.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.Midi.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.Wasapi.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\NAudio.WinMM.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\VS\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\libmp3lame.32.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\libmp3lame.64.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.Asio.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.Midi.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.Wasapi.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\NAudio.WinMM.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Config\DouBaoLLM.json
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Config\GuiJi.json
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Config\HuoShanTTSConfig.json
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\亲切女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\佩奇女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\倾心女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\傲娇女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\傲慢女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\儿童女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\元气甜妹.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\初恋女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\卿卿女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\可爱女生.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\台湾女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\呆萌川妹.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\和蔼奶奶.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\妩媚女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\妩媚御姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\妹坨洁儿.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\娇弱萝莉.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\娇憨女王.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\少儿故事.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\少御女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\开朗姐姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\心灵鸡汤.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\性感御姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\性感魅惑.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\成熟姐姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\撒娇学妹.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\文静毛毛.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\暖心学姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\暖阳女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\柔美女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\樱桃丸子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\活泼刁蛮.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\活泼女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\清新女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\清澈女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔婆婆.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔小卿.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔文雅.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔淑女.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\爽快女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜心小美.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜美小源.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜美悦悦.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜美桃子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\病娇姐姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\病娇萌妹.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\病弱少女.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\皇帝女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\知心姐姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\知性女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\知性温婉.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\纯澈女生.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\萌萌女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\调皮公主.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\调皮女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\贴心女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\贴心妹妹.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\贴心闺蜜.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邪魅女王.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邪魅御姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邻家女孩.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邻居阿姨.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\顾姐女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\高冷御姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\魅力女友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\魅力女声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\female\鸡汤姐姐.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\不羁青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\东方浩然.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\中二青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\京腔侃爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\亮嗓萌仔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\亲切青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\仗剑侠客.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\仗剑君子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\优柔公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\优柔帮主.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\低音沉郁.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\俊朗男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\俊逸公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲娇公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲娇精英.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲娇霸总.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲慢少爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲慢青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲气凌人.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅君子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅总裁.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅才俊.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\元气少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\光棍小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷傲总裁.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷峻上司.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷峻高智.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷淡疏离.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷漠男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷脸兄长.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷脸学霸.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷酷哥哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\凌云青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\北京小爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\反卷青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\咆哮小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\嚣张小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\四郎解说.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\固执病娇.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\天才公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\天才童声.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\奶气萌娃.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\孤傲公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\孤高公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\学霸同桌.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\学霸哥哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\孱弱少爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\小侯哥哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\少年将军.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\少年梓辛.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\干净少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\幽默叔叔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\幽默大爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\广告解说.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\广州德哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\广西远舟.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\开朗学长.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\开朗轻快.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\开朗青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\快乐小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\悠悠君子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\悬疑解说.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\意气少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\憨厚敦实.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\憨憨熊二.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\懒音绵宝.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\懵懂青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\成熟总裁.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\撒娇男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\撒娇男生.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\撒娇粘人.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\擎苍大爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\斯文青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\暖心体贴.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\机灵小伙.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\机甲智能.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\枕边低语.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\正直青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\油腻大叔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\洒脱青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活力小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活力青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活泼爽朗.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活泼男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\浩宇小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\深夜播客.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\深沉总裁.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\清爽少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\清爽男大.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\渊博小叔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温暖少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温暖阿虎.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔哥哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔学长.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温润学者.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温顺少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\湾区大叔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\潇洒随性.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\热血少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\爽朗少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\率真小伙.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\甜系男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇哥哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇弟弟.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇白莲.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病弱公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\直率青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\磁性男嗓.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\磁性解说.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\神秘法师.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\粘人男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\精英青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\纯真学弟.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\绿茶小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\美猴王.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\翩翩公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\胡子叔叔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\腹黑公子.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\自信青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\自负青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\解说小明.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\诡异神秘.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\豫州子轩.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\贴心男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\邻家男孩.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\醇厚小哥.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\醋精男友.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\醋精男生.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\阳光阿辰.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\阳光青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\霸气青叔.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\霸道少爷.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\青涩小生.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\青涩青年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\风发少年.wav
E:\VS\HumanVoice_BG\bin\Debug\net8.0\Audios\male\高冷总裁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\亲切女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\佩奇女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\倾心女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\傲娇女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\傲慢女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\儿童女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\元气甜妹.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\初恋女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\卿卿女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\可爱女生.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\台湾女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\呆萌川妹.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\和蔼奶奶.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\妩媚女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\妩媚御姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\妹坨洁儿.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\娇弱萝莉.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\娇憨女王.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\少儿故事.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\少御女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\开朗姐姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\心灵鸡汤.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\性感御姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\性感魅惑.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\成熟姐姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\撒娇学妹.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\文静毛毛.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\暖心学姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\暖阳女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\柔美女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\樱桃丸子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\活泼刁蛮.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\活泼女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\清新女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\清澈女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔婆婆.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔小卿.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔文雅.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\温柔淑女.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\爽快女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜心小美.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜美小源.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜美悦悦.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\甜美桃子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\病娇姐姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\病娇萌妹.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\病弱少女.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\皇帝女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\知心姐姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\知性女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\知性温婉.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\纯澈女生.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\萌萌女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\调皮公主.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\调皮女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\贴心女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\贴心妹妹.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\贴心闺蜜.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邪魅女王.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邪魅御姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邻家女孩.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\邻居阿姨.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\顾姐女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\高冷御姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\魅力女友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\魅力女声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\female\鸡汤姐姐.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\不羁青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\东方浩然.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\中二青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\京腔侃爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\亮嗓萌仔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\亲切青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\仗剑侠客.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\仗剑君子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\优柔公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\优柔帮主.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\低音沉郁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\俊朗男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\俊逸公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲娇公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲娇精英.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲娇霸总.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲慢少爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲慢青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\傲气凌人.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅君子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅总裁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅才俊.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\儒雅青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\元气少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\光棍小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷傲总裁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷峻上司.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷峻高智.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷淡疏离.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷漠男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷脸兄长.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷脸学霸.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\冷酷哥哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\凌云青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\北京小爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\反卷青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\咆哮小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\嚣张小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\四郎解说.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\固执病娇.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\天才公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\天才童声.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\奶气萌娃.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\孤傲公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\孤高公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\学霸同桌.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\学霸哥哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\孱弱少爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\小侯哥哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\少年将军.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\少年梓辛.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\干净少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\幽默叔叔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\幽默大爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\广告解说.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\广州德哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\广西远舟.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\开朗学长.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\开朗轻快.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\开朗青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\快乐小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\悠悠君子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\悬疑解说.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\意气少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\憨厚敦实.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\憨憨熊二.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\懒音绵宝.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\懵懂青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\成熟总裁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\撒娇男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\撒娇男生.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\撒娇粘人.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\擎苍大爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\斯文青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\暖心体贴.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\机灵小伙.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\机甲智能.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\枕边低语.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\正直青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\油腻大叔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\洒脱青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活力小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活力青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活泼爽朗.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\活泼男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\浩宇小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\深夜播客.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\深沉总裁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\清爽少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\清爽男大.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\渊博小叔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温暖少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温暖阿虎.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔哥哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔学长.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温柔男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温润学者.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\温顺少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\湾区大叔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\潇洒随性.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\热血少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\爽朗少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\率真小伙.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\甜系男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇哥哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇弟弟.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病娇白莲.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\病弱公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\直率青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\磁性男嗓.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\磁性解说.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\神秘法师.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\粘人男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\精英青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\纯真学弟.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\绿茶小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\美猴王.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\翩翩公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\胡子叔叔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\腹黑公子.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\自信青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\自负青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\解说小明.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\诡异神秘.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\豫州子轩.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\贴心男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\邻家男孩.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\醇厚小哥.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\醋精男友.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\醋精男生.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\阳光阿辰.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\阳光青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\霸气青叔.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\霸道少爷.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\青涩小生.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\青涩青年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\风发少年.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Audios\male\高冷总裁.wav
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Config\DouBaoLLM.json
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Config\GuiJi.json
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Config\HuoShanTTSConfig.json
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.exe
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.deps.json
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.runtimeconfig.json
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\HumanVoice_Backstage.pdb
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Azure.AI.OpenAI.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.Asio.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.Core.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\LameDLLWrap.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.Lame.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.Midi.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.Wasapi.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\NAudio.WinMM.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\OpenAI.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.AssemblyInfo.cs
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoi.D2AE34CB.Up2Date
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\refint\HumanVoice_Backstage.dll
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.pdb
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\HumanVoice_Backstage.genruntimeconfig.cache
C:\Users\<USER>\Desktop\P1022\HumanVoice_BG\obj\Debug\net8.0\ref\HumanVoice_Backstage.dll
