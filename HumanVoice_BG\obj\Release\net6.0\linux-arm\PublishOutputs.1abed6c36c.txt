E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\Model\en_2024_03_09\model.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\Model\en_2024_03_09\tokens.txt
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\Model\vad\silero_vad.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\publish\linux-x64\HumanVoice_Backstage
