﻿using HumanVoice_Backstage.Config;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace HumanVoice_Backstage.LLM
{
    internal class GuiJiVLM : LLMBase
    {
        [Serializable]
        public class UserDataStruct
        {
            public string model;
            public bool stream;
            public float temperature;

            public List<Message> messages;
        }

        [Serializable]
        public class Message
        {
            private Message() { }

            public string role;
            public Content[] content;

            public static Message CreateSystemMessage(string content)
            {
                return new Message()
                {
                    role = "system",
                    content = new Content[]{
                    new Content
                    {
                         text = content,
                          type="text"
                    }
                }
                };
            }

            public static Message CreateUserMessage(string content)
            {
                return new Message()
                {
                    role = "user",
                    content = new Content[]{
                    new Content
                    {
                         text = content,
                          type="text"
                    }
                }
                };
            }
            public static Message CreateUserAndImageMessage(string content, string image)
            {
                return new Message()
                {
                    role = "user",
                    content = new Content[]{
                    new Content
                    {
                         image_url=new Image_Url
                         {
                              detail="auto",
                            //   url="data:image/jpg;base64,"+image
                               url= image
                         },
                          type="image_url"
                    },
                       new Content
                    {
                         text = content,
                          type="text"
                    }
                }
                };
            }

            public static Message CreateAssistantMessage(string content)
            {
                return new Message()
                {
                    role = "assistant",
                    content = new Content[]{
                    new Content
                    {
                         text = content,
                          type="text"
                    }
                }
                };
            }
        }

        [Serializable]
        public class Content
        {
            public Image_Url image_url;
            public string type;
            public string text;
        }
        [Serializable]
        public class Image_Url
        {
            public string detail;
            public string url;
        }


        const string uri = "https://api.siliconflow.cn/v1/chat/completions";
         

          UserDataStruct userDataStruct;

        static LLMConfig config;
        static  GuiJiVLM()
        {
            config = JsonConvert.DeserializeObject< LLMConfig>(File.ReadAllText("Config/GuiJi.json")); 
        }

        public override void Init(string systemMessage = null)
        {
            userDataStruct = new UserDataStruct()
            {
                model = config.model,
                stream = true,
                messages = new List<Message>(),
                temperature = 0.7f,

            };
            userDataStruct.messages.Add(Message.CreateSystemMessage(systemMessage));
        }

        public override void RequestGPT(string prompt, Action<string, bool> callback)
        {
            
            try
            {
                if (prompt.Contains("|"))
                {
                    var ss = prompt.Split('|');

                    if (ss.Length == 2)
                    {
                        userDataStruct.messages.Add(Message.CreateUserAndImageMessage( ss[1], ss[0]));

                    }
                    else
                    {
                        userDataStruct.messages.Add(Message.CreateUserMessage(prompt));
                    }
                }
                else
                {
                    userDataStruct.messages.Add(Message.CreateUserMessage(prompt));
                }

                var requestKimiJson = Newtonsoft.Json.JsonConvert.SerializeObject (userDataStruct);
                
                HttpWebRequest httpWebRequest = HttpWebRequest.Create(uri) as HttpWebRequest;
                httpWebRequest.Method = "POST";
                httpWebRequest.ContentType = "application/json";
                httpWebRequest.Headers.Add("Authorization", "Bearer " + config.key);

                using (var requestSt = httpWebRequest.GetRequestStream())
                {
                    var buffer = Encoding.UTF8.GetBytes(requestKimiJson);
                    requestSt.Write(buffer, 0, buffer.Length);
                }

                var response = httpWebRequest.GetResponse();
                using var responseSt = response.GetResponseStream();
                using StreamReader resposneSTR = new StreamReader(responseSt);
                string mess = "";
                while (!resposneSTR.EndOfStream)
                {
                    requestKimiJson = resposneSTR.ReadLine();
                    if (requestKimiJson.StartsWith("data:"))
                    {
                        if (requestKimiJson.Contains("[DONE]"))
                            break;
                        var jsonP = JToken.Parse(requestKimiJson.Replace("data:", ""));
                        var item = jsonP["choices"][0];

                        var tt = item["delta"].SelectToken("content")?.ToString();

                        if (!string.IsNullOrEmpty(tt))
                        {
                            callback(tt, false);
                            mess += tt;
                        }
                        var finish = item.SelectToken("finish_reason");

                        if (finish != null && finish.ToString() == "stop")
                        {
                            break;
                        }
                    }
                }

                callback("", true);

                if (!string.IsNullOrEmpty(mess))
                {

                    userDataStruct.messages.Add(Message.CreateAssistantMessage(mess));
                }
                else
                { 
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            UpdateRemoveMessages();
        }


        private void UpdateRemoveMessages()
        {
            while (userDataStruct.messages.Count > 10)
            {
                userDataStruct.messages.RemoveAt(1);
            }
        }
    }
}
