E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\sherpa-onnx.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libonnxruntime.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libsherpa-onnx-c-api.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.AppContext.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Buffers.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Configuration.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Console.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Core.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Data.Common.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Data.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Drawing.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Globalization.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Memory.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Http.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Security.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Numerics.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Json.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Transactions.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Web.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Windows.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\WindowsBase.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\mscorlib.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\netstandard.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\createdump
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Native.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libclrgc.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libclrjit.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libcoreclr.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libhostfxr.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libhostpolicy.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libmscordaccore.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libmscordbi.so
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
E:\VS\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\model.onnx
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\tokens.txt
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Model\vad\silero_vad.onnx
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Azure.Core.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\OpenAI.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ClientModel.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\model.onnx
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\tokens.txt
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Model\vad\silero_vad.onnx
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Azure.Core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\OpenAI.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\sherpa-onnx.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ClientModel.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libonnxruntime.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libsherpa-onnx-c-api.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.AppContext.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Buffers.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Collections.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Configuration.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Console.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Core.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Data.Common.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Data.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Drawing.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Globalization.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.IO.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Linq.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Memory.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Http.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Security.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Net.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Numerics.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Reflection.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Runtime.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Security.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.Json.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Threading.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Transactions.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Web.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Windows.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.Xml.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\System.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\WindowsBase.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\mscorlib.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\netstandard.dll
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\createdump
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Native.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libclrgc.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libclrjit.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libcoreclr.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libhostfxr.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libhostpolicy.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libmscordaccore.so
E:\Unity\Human_Back\HumanVoice_Backstage\bin\Release\net8.0\linux-x64\libmscordbi.so
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
E:\Unity\Human_Back\HumanVoice_Backstage\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Model\vad\silero_vad.onnx
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.Core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\OpenAI.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\sherpa-onnx.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ClientModel.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libonnxruntime.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libsherpa-onnx-c-api.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.AppContext.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Buffers.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Configuration.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Console.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.Common.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Security.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Json.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Windows.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\WindowsBase.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\mscorlib.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\netstandard.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\createdump
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Native.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrgc.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrjit.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclr.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostfxr.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostpolicy.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordaccore.so
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordbi.so
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
E:\Unity\Human_Back\HumanVoice_BG\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmp3lame.32.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmp3lame.64.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Core.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\LameDLLWrap.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Lame.dll
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Model\en_2024_03_09\model.onnx
E:\Unity\Human_Back\HumanVoice_BG\bin\Release\net8.0\linux-x64\Model\en_2024_03_09\tokens.txt
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Model\en_2024_03_09\model.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Model\en_2024_03_09\tokens.txt
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Model\vad\silero_vad.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Asio.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\LameDLLWrap.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Lame.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Midi.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Wasapi.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.WinMM.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\OpenAI.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\sherpa-onnx.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ClientModel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libonnxruntime.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libsherpa-onnx-c-api.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.AppContext.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Buffers.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Configuration.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Console.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.Common.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Security.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Json.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Windows.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\WindowsBase.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\mscorlib.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\netstandard.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\createdump
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Native.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrgc.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrjit.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclr.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostfxr.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostpolicy.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordaccore.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordbi.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Asio.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\LameDLLWrap.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Lame.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Midi.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Wasapi.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.WinMM.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\OpenAI.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ClientModel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.AppContext.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Buffers.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Configuration.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Console.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.Common.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Security.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Json.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Windows.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\WindowsBase.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\mscorlib.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\netstandard.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\createdump
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Native.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrgc.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrjit.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclr.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostfxr.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostpolicy.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordaccore.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordbi.so
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.Core.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Asio.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Core.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\LameDLLWrap.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Lame.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Midi.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Wasapi.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.WinMM.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\OpenAI.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ClientModel.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.AppContext.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Buffers.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Configuration.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Console.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Core.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.Common.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Security.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Json.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Windows.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\WindowsBase.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\mscorlib.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\netstandard.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\createdump
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Native.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrgc.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrjit.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclr.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostfxr.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostpolicy.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordaccore.so
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordbi.so
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
E:\VS\HumanVoice_BG\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmp3lame.32.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmp3lame.64.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.deps.json
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.runtimeconfig.json
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.AI.OpenAI.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Azure.Core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Asio.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\LameDLLWrap.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Lame.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Midi.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.Wasapi.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\NAudio.WinMM.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\OpenAI.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ClientModel.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.Data.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.CSharp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.VisualBasic.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\Microsoft.Win32.Registry.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.AppContext.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Buffers.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Concurrent.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Immutable.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.NonGeneric.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.Specialized.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Collections.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ComponentModel.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Configuration.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Console.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Core.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.Common.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.DataSetExtensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Data.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Debug.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Process.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tools.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Drawing.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Dynamic.Runtime.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Asn1.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Formats.Tar.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Calendars.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.Extensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Globalization.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.Brotli.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Compression.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.FileSystem.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.Pipes.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.IO.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Expressions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Parallel.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.Queryable.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Linq.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Memory.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.Json.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Http.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.HttpListener.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Mail.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NameResolution.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.NetworkInformation.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Ping.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Quic.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Requests.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Security.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.ServicePoint.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.Sockets.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebClient.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebProxy.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.WebSockets.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Net.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.Vectors.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Numerics.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ObjectModel.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.CoreLib.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.DataContractSerialization.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Uri.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.Linq.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Private.Xml.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Emit.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Extensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Metadata.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Reflection.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Reader.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.ResourceManager.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Resources.Writer.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Extensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Handles.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.InteropServices.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Intrinsics.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Loader.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Numerics.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.Serialization.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Runtime.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.AccessControl.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Claims.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Cryptography.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.Windows.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.Principal.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.SecureString.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Security.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceModel.Web.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ServiceProcess.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encoding.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Encodings.Web.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.Json.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Text.RegularExpressions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Channels.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Overlapped.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Tasks.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Thread.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.ThreadPool.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.Timer.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Threading.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.Local.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Transactions.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.ValueTuple.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.HttpUtility.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Web.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Windows.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Linq.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.Serialization.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XDocument.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XPath.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlDocument.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.Xml.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\System.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\WindowsBase.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\mscorlib.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\netstandard.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\createdump
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Globalization.Native.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.IO.Compression.Native.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Native.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Net.Security.Native.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libSystem.Security.Cryptography.Native.OpenSsl.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrgc.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libclrjit.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclr.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libcoreclrtraceptprovider.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostfxr.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libhostpolicy.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordaccore.so
C:\Users\<USER>\Downloads\HumanVoice_BG\bin\Release\net8.0\linux-x64\libmscordbi.so
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfoInputs.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.AssemblyInfo.cs
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoi.D2AE34CB.Up2Date
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\refint\HumanVoice_Backstage.dll
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.pdb
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\HumanVoice_Backstage.genruntimeconfig.cache
C:\Users\<USER>\Downloads\HumanVoice_BG\obj\Release\net8.0\linux-x64\ref\HumanVoice_Backstage.dll
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Config\DouBaoLLM.json
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Config\GuiJi.json
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Config\HuoShanTTSConfig.json
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\亲切女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\佩奇女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\倾心女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\傲娇女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\傲慢女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\儿童女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\元气甜妹.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\初恋女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\卿卿女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\可爱女生.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\台湾女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\呆萌川妹.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\和蔼奶奶.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\妩媚女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\妩媚御姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\妹坨洁儿.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\娇弱萝莉.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\娇憨女王.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\少儿故事.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\少御女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\开朗姐姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\心灵鸡汤.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\性感御姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\性感魅惑.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\成熟姐姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\撒娇学妹.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\文静毛毛.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\暖心学姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\暖阳女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\柔美女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\樱桃丸子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\活泼刁蛮.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\活泼女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\清新女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\清澈女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\温柔女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\温柔婆婆.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\温柔小卿.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\温柔文雅.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\温柔淑女.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\爽快女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\甜心小美.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\甜美小源.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\甜美悦悦.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\甜美桃子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\病娇姐姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\病娇萌妹.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\病弱少女.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\皇帝女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\知心姐姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\知性女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\知性温婉.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\纯澈女生.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\萌萌女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\调皮公主.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\调皮女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\贴心女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\贴心妹妹.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\贴心闺蜜.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\邪魅女王.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\邪魅御姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\邻家女孩.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\邻居阿姨.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\顾姐女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\高冷御姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\魅力女友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\魅力女声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\female\鸡汤姐姐.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\不羁青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\东方浩然.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\中二青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\京腔侃爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\亮嗓萌仔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\亲切青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\仗剑侠客.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\仗剑君子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\优柔公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\优柔帮主.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\低音沉郁.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\俊朗男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\俊逸公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\傲娇公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\傲娇精英.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\傲娇霸总.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\傲慢少爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\傲慢青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\傲气凌人.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\儒雅公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\儒雅君子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\儒雅总裁.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\儒雅才俊.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\儒雅男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\儒雅青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\元气少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\光棍小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷傲总裁.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷峻上司.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷峻高智.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷淡疏离.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷漠男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷脸兄长.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷脸学霸.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\冷酷哥哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\凌云青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\北京小爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\反卷青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\咆哮小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\嚣张小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\四郎解说.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\固执病娇.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\天才公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\天才童声.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\奶气萌娃.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\孤傲公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\孤高公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\学霸同桌.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\学霸哥哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\孱弱少爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\小侯哥哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\少年将军.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\少年梓辛.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\干净少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\幽默叔叔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\幽默大爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\广告解说.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\广州德哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\广西远舟.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\开朗学长.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\开朗轻快.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\开朗青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\快乐小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\悠悠君子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\悬疑解说.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\意气少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\憨厚敦实.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\憨憨熊二.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\懒音绵宝.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\懵懂青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\成熟总裁.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\撒娇男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\撒娇男生.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\撒娇粘人.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\擎苍大爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\斯文青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\暖心体贴.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\机灵小伙.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\机甲智能.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\枕边低语.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\正直青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\油腻大叔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\洒脱青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\活力小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\活力青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\活泼爽朗.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\活泼男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\浩宇小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\深夜播客.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\深沉总裁.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\清爽少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\清爽男大.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\渊博小叔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温暖少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温暖阿虎.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温柔哥哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温柔学长.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温柔小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温柔男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温润学者.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\温顺少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\湾区大叔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\潇洒随性.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\热血少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\爽朗少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\率真小伙.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\甜系男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\病娇哥哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\病娇少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\病娇弟弟.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\病娇白莲.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\病弱公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\直率青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\磁性男嗓.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\磁性解说.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\神秘法师.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\粘人男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\精英青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\纯真学弟.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\绿茶小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\美猴王.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\翩翩公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\胡子叔叔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\腹黑公子.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\自信青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\自负青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\解说小明.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\诡异神秘.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\豫州子轩.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\贴心男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\邻家男孩.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\醇厚小哥.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\醋精男友.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\醋精男生.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\阳光阿辰.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\阳光青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\霸气青叔.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\霸道少爷.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\青涩小生.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\青涩青年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\风发少年.wav
E:\VS\HumanVoice_BG\bin\Release\net8.0\linux-x64\Audios\male\高冷总裁.wav
