{"format": 1, "restore": {"E:\\unity\\YeHe_VoiceHuman\\HumanVoice_BG\\HumanVoice_Backstage.csproj": {}}, "projects": {"E:\\unity\\YeHe_VoiceHuman\\HumanVoice_BG\\HumanVoice_Backstage.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\unity\\YeHe_VoiceHuman\\HumanVoice_BG\\HumanVoice_Backstage.csproj", "projectName": "HumanVoice_Backstage", "projectPath": "E:\\unity\\YeHe_VoiceHuman\\HumanVoice_BG\\HumanVoice_Backstage.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\unity\\YeHe_VoiceHuman\\HumanVoice_BG\\obj\\publish\\linux-arm\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\vs\\gongxiang\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Azure.AI.OpenAI": {"target": "Package", "version": "[2.0.0-beta.2, )"}, "Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.42.0, )"}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23401.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23401.1, )", "autoReferenced": true}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "org.k2fsa.sherpa.onnx": {"target": "Package", "version": "[1.10.22, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Host.linux-arm", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400\\RuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm": {"#import": []}}}}}