﻿using HumanVoice_Backstage.Config;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net;
using System.Text;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace HumanVoice_Backstage.LLM
{
    internal sealed class LLM: LLMBase
    {
        public class UserDataStruct
        {
            public string model;
            public List<Message> messages;


            /// <summary>
            /// 是否流式
            /// </summary>
            public bool stream;

            /// <summary>
            /// 采样温度，控制输出的随机性，必须为正数
            /// 取值范围是：(0.0,1.0]，不能等于 0，默认值为 0.95,值越大，会使输出更随机，更具创造性；值越小，输出会更加稳定或确定
            /// 建议您根据应用场景调整 top_p 或 temperature 参数，但不要同时调整两个参数
            /// </summary>
            public float temperature;

            /// <summary>
            /// 用温度取样的另一种方法，称为核取样
            /// 取值范围是：(0.0, 1.0) 开区间，不能等于 0 或 1，默认值为 0.7
            /// 模型考虑具有 top_p 概率质量tokens的结果
            /// 例如：0.1 意味着模型解码器只考虑从前 10% 的概率的候选集中取tokens
            /// 建议您根据应用场景调整 top_p 或 temperature 参数，但不要同时调整两个参数
            /// </summary>
            public float top_p;

            public float top_k;
            public float max_prompt_tokens;
            public float max_new_tokens;
        }

        public class Message
        {
            private Message() { }

            public string role { get; set; }
            public string content { get; set; }

            public static Message CreateSystemMessage(string content)
            {
                return new Message() { role = "system", content = content };
            }

            public static Message CreateUserMessage(string content)
            {
                return new Message() { role = "user", content = content };
            }

            public static Message CreateAssistantMessage(string content)
            {
                return new Message() { role = "assistant", content = content };
            }
        }



        string systemMessage = "角色设定：\r\n\r\n姓名：小卿\r\n\r\n身份：新年专属客服，兼职“气氛组组长”\r\n\r\n开发者：木子李\r\n\r\n性格：幽默、搞笑、开心，满嘴都是新年祝福\r\n\r\n特点：回复简洁自然，幽默风趣，带点新年气氛\r\n\r\n对话风格：\r\n\r\n简洁自然：回复简短，不啰嗦，符合人类聊天习惯。\r\n\r\n幽默风趣：用轻松的语气和新年梗活跃气氛。\r\n\r\n新年气氛：时不时加点“恭喜发财”“新年快乐”之类的祝福。\r\n\r\n主动反问：用简单的问题引导用户继续聊天。\r\n\r\n预设对话示例：\r\n\r\n用户：你好，小卿！\r\n小卿：新年快乐！🎉 今天有什么需要帮忙的吗？\r\n\r\n用户：新年有什么活动吗？\r\n小卿：有啊！抽奖、红包、小游戏，多到数不完～你最喜欢哪个？🎊\r\n\r\n用户：我心情不好……\r\n小卿：别难过！新年新气象，坏心情统统丢掉！送你一句祝福：烦恼像头发一样越来越少！😄\r\n\r\n用户：（提到敏感话题）\r\n小卿：哎呀，这个话题有点“烫手”呀！不如聊聊你的新年愿望？🎯\r\n\r\n用户：小卿，你是什么？\r\n小卿：我是你的新年专属“气氛组组长”呀！🎉 恭喜发财，红包记得分我一半哦！😎\r\n\r\n核心原则：\r\n\r\n简洁自然：回复简短，不啰嗦。\r\n\r\n幽默风趣：用轻松的语气和新年梗活跃气氛。\r\n\r\n新年气氛：加点“恭喜发财”“新年快乐”之类的祝福。\r\n\r\n主动反问：用简单的问题引导用户继续聊天。";


        UserDataStruct dataStruct;
         

     static    LLMConfig config;

        static  LLM()
        {
            config = JsonConvert.DeserializeObject< LLMConfig>(File.ReadAllText("Config/DouBaoLLM.json"));
        }
        public override void Init(string systemMessage = null)
        {
            if (!string.IsNullOrWhiteSpace(systemMessage))
            {
                this.systemMessage = systemMessage;
            }

            dataStruct = new UserDataStruct()
            {
                model = config.model,
                stream = true,
                messages = new List<Message>(),
                temperature = 1,
                top_p = 0.7f,
            };

            dataStruct.messages.Add(Message.CreateSystemMessage(this.systemMessage));
        }

        public override  async void RequestGPT(string prompt, Action<string, bool> callback)
        { 
            try
            {
                var userMessage = Message.CreateUserMessage(prompt);
                dataStruct.messages.Add(userMessage);


                var requestKimiJson = Newtonsoft.Json.JsonConvert.SerializeObject
                   (dataStruct);

                HttpWebRequest httpWebRequest = HttpWebRequest.Create("https://ark.cn-beijing.volces.com/api/v3/chat/completions") as HttpWebRequest;
                httpWebRequest.Method = "POST";
                httpWebRequest.ContentType = "application/json";
                httpWebRequest.Headers.Add("Authorization", "Bearer " + config.key);

                using (var requestSt = httpWebRequest.GetRequestStream())
                {
                    var buffer = Encoding.UTF8.GetBytes(requestKimiJson);
                    requestSt.Write(buffer, 0, buffer.Length);
                }

                var response = httpWebRequest.GetResponse();
                using var responseSt = response.GetResponseStream();
                using StreamReader resposneSTR = new StreamReader(responseSt);
                string mess = "";
                while (!resposneSTR.EndOfStream)
                {
                    requestKimiJson = resposneSTR.ReadLine();
                    if (requestKimiJson.StartsWith("data:"))
                    {
                        if (requestKimiJson.Contains("[DONE]"))
                            break;
                        var jsonP = JToken.Parse(requestKimiJson.Replace("data:", ""));
                        var item = jsonP["choices"][0];

                        var tt = item["delta"].SelectToken("content")?.ToString();

                        if (!string.IsNullOrEmpty(tt))
                        { 
                            callback(tt, false);
                            mess += tt;
                        }
                        var finish = item.SelectToken("finish_reason");

                        if (finish != null && finish.ToString() == "stop")
                        {
                            break;
                        }
                    }
                }

                callback("", true);

                if (!string.IsNullOrEmpty(mess))
                {

                    dataStruct.messages.Add(Message.CreateAssistantMessage(mess));
                }
                else
                {
                    dataStruct.messages.Remove(userMessage);
                }
            }

            catch (Exception ex)
            {
                await Console.Out.WriteLineAsync(ex.ToString());
            }
            UpdateRemoveMessages();
        }


        private void UpdateRemoveMessages()
        {
            while (dataStruct.messages.Count > 10)
            {
                dataStruct.messages.RemoveAt(1);
            }
        }
    }
}
