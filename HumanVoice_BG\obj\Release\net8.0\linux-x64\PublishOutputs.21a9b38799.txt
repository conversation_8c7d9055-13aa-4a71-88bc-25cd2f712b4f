E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\HumanVoice_Backstage.pdb
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\model.onnx
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\Model\sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17\tokens.txt
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\Model\vad\silero_vad.onnx
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.core.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libpal_azure_c_shared.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libpal_azure_c_shared_openssl3.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libonnxruntime.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\libsherpa-onnx-c-api.so
E:\VS\HumanVoice_Backstage\bin\Release\net8.0\publish\linux-x64\HumanVoice_Backstage
