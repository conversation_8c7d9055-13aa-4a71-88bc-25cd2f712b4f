﻿++解决方案 'HumanVoice_Backstage' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:HumanVoice_Backstage.sln
++HumanVoice_Backstage
i:{00000000-0000-0000-0000-000000000000}:HumanVoice_Backstage
++Properties
i:{************************************}:e:\vs\humanvoice_bg\properties\
++依赖项
i:{************************************}:>3682
++Audios
i:{************************************}:e:\vs\humanvoice_bg\audios\
++female
i:{************************************}:e:\vs\humanvoice_bg\audios\female\
++傲娇女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\傲娇女友.wav
++傲慢女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\傲慢女友.wav
++病娇姐姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\病娇姐姐.wav
++病娇萌妹.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\病娇萌妹.wav
++病弱少女.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\病弱少女.wav
++成熟姐姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\成熟姐姐.wav
++初恋女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\初恋女友.wav
++纯澈女生.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\纯澈女生.wav
++呆萌川妹.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\呆萌川妹.wav
++调皮公主.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\调皮公主.wav
++调皮女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\调皮女声.wav
++儿童女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\儿童女声.wav
++高冷御姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\高冷御姐.wav
++顾姐女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\顾姐女声.wav
++和蔼奶奶.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\和蔼奶奶.wav
++皇帝女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\皇帝女声.wav
++活泼刁蛮.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\活泼刁蛮.wav
++活泼女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\活泼女友.wav
++鸡汤姐姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\鸡汤姐姐.wav
++娇憨女王.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\娇憨女王.wav
++娇弱萝莉.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\娇弱萝莉.wav
++开朗姐姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\开朗姐姐.wav
++可爱女生.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\可爱女生.wav
++邻家女孩.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\邻家女孩.wav
++邻居阿姨.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\邻居阿姨.wav
++妹坨洁儿.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\妹坨洁儿.wav
++魅力女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\魅力女声.wav
++魅力女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\魅力女友.wav
++萌萌女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\萌萌女友.wav
++暖心学姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\暖心学姐.wav
++暖阳女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\暖阳女声.wav
++佩奇女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\佩奇女声.wav
++亲切女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\亲切女声.wav
++倾心女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\倾心女声.wav
++卿卿女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\卿卿女声.wav
++清澈女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\清澈女友.wav
++清新女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\清新女友.wav
++柔美女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\柔美女友.wav
++撒娇学妹.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\撒娇学妹.wav
++少儿故事.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\少儿故事.wav
++少御女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\少御女友.wav
++爽快女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\爽快女友.wav
++台湾女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\台湾女声.wav
++甜美桃子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\甜美桃子.wav
++甜美小源.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\甜美小源.wav
++甜美悦悦.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\甜美悦悦.wav
++甜心小美.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\甜心小美.wav
++贴心闺蜜.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\贴心闺蜜.wav
++贴心妹妹.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\贴心妹妹.wav
++贴心女友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\贴心女友.wav
++温柔女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\温柔女声.wav
++温柔婆婆.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\温柔婆婆.wav
++温柔淑女.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\温柔淑女.wav
++温柔文雅.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\温柔文雅.wav
++温柔小卿.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\温柔小卿.wav
++文静毛毛.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\文静毛毛.wav
++妩媚女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\妩媚女声.wav
++妩媚御姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\妩媚御姐.wav
++邪魅女王.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\邪魅女王.wav
++邪魅御姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\邪魅御姐.wav
++心灵鸡汤.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\心灵鸡汤.wav
++性感魅惑.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\性感魅惑.wav
++性感御姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\性感御姐.wav
++樱桃丸子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\樱桃丸子.wav
++元气甜妹.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\元气甜妹.wav
++知心姐姐.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\知心姐姐.wav
++知性女声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\知性女声.wav
++知性温婉.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\female\知性温婉.wav
++male
i:{************************************}:e:\vs\humanvoice_bg\audios\male\
++傲娇霸总.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\傲娇霸总.wav
++傲娇公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\傲娇公子.wav
++傲娇精英.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\傲娇精英.wav
++傲慢青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\傲慢青年.wav
++傲慢少爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\傲慢少爷.wav
++傲气凌人.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\傲气凌人.wav
++霸道少爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\霸道少爷.wav
++霸气青叔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\霸气青叔.wav
++北京小爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\北京小爷.wav
++病娇白莲.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\病娇白莲.wav
++病娇弟弟.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\病娇弟弟.wav
++病娇哥哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\病娇哥哥.wav
++病娇少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\病娇少年.wav
++病弱公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\病弱公子.wav
++不羁青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\不羁青年.wav
++孱弱少爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\孱弱少爷.wav
++成熟总裁.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\成熟总裁.wav
++纯真学弟.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\纯真学弟.wav
++醇厚小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\醇厚小哥.wav
++磁性解说.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\磁性解说.wav
++磁性男嗓.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\磁性男嗓.wav
++醋精男生.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\醋精男生.wav
++醋精男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\醋精男友.wav
++低音沉郁.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\低音沉郁.wav
++东方浩然.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\东方浩然.wav
++反卷青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\反卷青年.wav
++风发少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\风发少年.wav
++腹黑公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\腹黑公子.wav
++干净少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\干净少年.wav
++高冷总裁.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\高冷总裁.wav
++孤傲公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\孤傲公子.wav
++孤高公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\孤高公子.wav
++固执病娇.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\固执病娇.wav
++光棍小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\光棍小哥.wav
++广告解说.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\广告解说.wav
++广西远舟.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\广西远舟.wav
++广州德哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\广州德哥.wav
++诡异神秘.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\诡异神秘.wav
++憨憨熊二.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\憨憨熊二.wav
++憨厚敦实.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\憨厚敦实.wav
++浩宇小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\浩宇小哥.wav
++胡子叔叔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\胡子叔叔.wav
++活力青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\活力青年.wav
++活力小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\活力小哥.wav
++活泼男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\活泼男友.wav
++活泼爽朗.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\活泼爽朗.wav
++机甲智能.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\机甲智能.wav
++机灵小伙.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\机灵小伙.wav
++解说小明.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\解说小明.wav
++京腔侃爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\京腔侃爷.wav
++精英青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\精英青年.wav
++俊朗男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\俊朗男友.wav
++俊逸公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\俊逸公子.wav
++开朗青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\开朗青年.wav
++开朗轻快.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\开朗轻快.wav
++开朗学长.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\开朗学长.wav
++快乐小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\快乐小哥.wav
++懒音绵宝.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\懒音绵宝.wav
++冷傲总裁.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷傲总裁.wav
++冷淡疏离.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷淡疏离.wav
++冷峻高智.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷峻高智.wav
++冷峻上司.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷峻上司.wav
++冷酷哥哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷酷哥哥.wav
++冷脸兄长.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷脸兄长.wav
++冷脸学霸.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷脸学霸.wav
++冷漠男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\冷漠男友.wav
++亮嗓萌仔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\亮嗓萌仔.wav
++邻家男孩.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\邻家男孩.wav
++凌云青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\凌云青年.wav
++率真小伙.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\率真小伙.wav
++绿茶小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\绿茶小哥.wav
++美猴王.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\美猴王.wav
++懵懂青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\懵懂青年.wav
++奶气萌娃.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\奶气萌娃.wav
++暖心体贴.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\暖心体贴.wav
++咆哮小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\咆哮小哥.wav
++翩翩公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\翩翩公子.wav
++亲切青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\亲切青年.wav
++青涩青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\青涩青年.wav
++青涩小生.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\青涩小生.wav
++清爽男大.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\清爽男大.wav
++清爽少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\清爽少年.wav
++擎苍大爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\擎苍大爷.wav
++热血少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\热血少年.wav
++儒雅才俊.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\儒雅才俊.wav
++儒雅公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\儒雅公子.wav
++儒雅君子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\儒雅君子.wav
++儒雅男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\儒雅男友.wav
++儒雅青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\儒雅青年.wav
++儒雅总裁.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\儒雅总裁.wav
++撒娇男生.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\撒娇男生.wav
++撒娇男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\撒娇男友.wav
++撒娇粘人.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\撒娇粘人.wav
++洒脱青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\洒脱青年.wav
++少年将军.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\少年将军.wav
++少年梓辛.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\少年梓辛.wav
++深沉总裁.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\深沉总裁.wav
++深夜播客.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\深夜播客.wav
++神秘法师.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\神秘法师.wav
++爽朗少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\爽朗少年.wav
++斯文青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\斯文青年.wav
++四郎解说.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\四郎解说.wav
++天才公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\天才公子.wav
++天才童声.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\天才童声.wav
++甜系男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\甜系男友.wav
++贴心男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\贴心男友.wav
++湾区大叔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\湾区大叔.wav
++温暖阿虎.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温暖阿虎.wav
++温暖少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温暖少年.wav
++温柔哥哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温柔哥哥.wav
++温柔男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温柔男友.wav
++温柔小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温柔小哥.wav
++温柔学长.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温柔学长.wav
++温润学者.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温润学者.wav
++温顺少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\温顺少年.wav
++潇洒随性.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\潇洒随性.wav
++嚣张小哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\嚣张小哥.wav
++小侯哥哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\小侯哥哥.wav
++悬疑解说.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\悬疑解说.wav
++学霸哥哥.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\学霸哥哥.wav
++学霸同桌.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\学霸同桌.wav
++阳光阿辰.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\阳光阿辰.wav
++阳光青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\阳光青年.wav
++意气少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\意气少年.wav
++优柔帮主.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\优柔帮主.wav
++优柔公子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\优柔公子.wav
++幽默大爷.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\幽默大爷.wav
++幽默叔叔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\幽默叔叔.wav
++悠悠君子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\悠悠君子.wav
++油腻大叔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\油腻大叔.wav
++豫州子轩.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\豫州子轩.wav
++渊博小叔.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\渊博小叔.wav
++元气少年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\元气少年.wav
++粘人男友.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\粘人男友.wav
++仗剑君子.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\仗剑君子.wav
++仗剑侠客.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\仗剑侠客.wav
++枕边低语.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\枕边低语.wav
++正直青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\正直青年.wav
++直率青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\直率青年.wav
++中二青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\中二青年.wav
++自负青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\自负青年.wav
++自信青年.wav
i:{************************************}:e:\vs\humanvoice_bg\audios\male\自信青年.wav
++Communication
i:{************************************}:e:\vs\humanvoice_bg\communication\
++DisponseData
i:{************************************}:e:\vs\humanvoice_bg\communication\disponsedata\
++Struct
i:{************************************}:e:\vs\humanvoice_bg\communication\disponsedata\struct\
++WebSocketClientDisponseData.cs
i:{************************************}:e:\vs\humanvoice_bg\communication\disponsedata\websocketclientdisponsedata.cs
++SocketCommunication.cs
i:{************************************}:e:\vs\humanvoice_bg\communication\socketcommunication.cs
++Config
i:{************************************}:e:\vs\humanvoice_bg\config\
++DouBaoLLM.json
i:{************************************}:e:\vs\humanvoice_bg\config\doubaollm.json
++GuiJi.json
i:{************************************}:e:\vs\humanvoice_bg\config\guiji.json
++HuoShanTTSConfig.cs
i:{************************************}:e:\vs\humanvoice_bg\config\huoshanttsconfig.cs
++HuoShanTTSConfig.json
i:{************************************}:e:\vs\humanvoice_bg\config\huoshanttsconfig.json
++LLMConfig.cs
i:{************************************}:e:\vs\humanvoice_bg\config\llmconfig.cs
++LLM
i:{************************************}:e:\vs\humanvoice_bg\llm\
++CozeLLM.cs
i:{************************************}:e:\vs\humanvoice_bg\llm\cozellm.cs
++GuiJiVLM.cs
i:{************************************}:e:\vs\humanvoice_bg\llm\guijivlm.cs
++LLM.cs
i:{************************************}:e:\vs\humanvoice_bg\llm\llm.cs
++LLMBase.cs
i:{************************************}:e:\vs\humanvoice_bg\llm\llmbase.cs
++TTS
i:{************************************}:e:\vs\humanvoice_bg\tts\
++TTS.cs
i:{************************************}:e:\vs\humanvoice_bg\tts\tts.cs
++Dockerfile
i:{************************************}:e:\vs\humanvoice_bg\dockerfile
++HumanVoice_BG.7z
i:{************************************}:e:\vs\humanvoice_bg\humanvoice_bg.7z
++libmp3lame.32.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\naudio.lame.crossplatform\2.2.1\contentfiles\any\net8.0\libmp3lame.32.dll
++libmp3lame.64.dll
i:{************************************}:c:\users\<USER>\.nuget\packages\naudio.lame.crossplatform\2.2.1\contentfiles\any\net8.0\libmp3lame.64.dll
++Program.cs
i:{************************************}:e:\vs\humanvoice_bg\program.cs
++PublishProfiles
i:{************************************}:e:\vs\humanvoice_bg\properties\publishprofiles\
++launchSettings.json
i:{************************************}:e:\vs\humanvoice_bg\properties\launchsettings.json
++包
i:{************************************}:>3694
++分析器
i:{************************************}:>3683
++框架
i:{************************************}:>3692
++DataStructRoot.cs
i:{************************************}:e:\vs\humanvoice_bg\communication\disponsedata\struct\datastructroot.cs
++TimeData.cs
i:{************************************}:e:\vs\humanvoice_bg\communication\disponsedata\struct\timedata.cs
++TTSData.cs
i:{************************************}:e:\vs\humanvoice_bg\communication\disponsedata\struct\ttsdata.cs
++.dockerignore
i:{************************************}:e:\vs\humanvoice_bg\.dockerignore
++FolderProfile.pubxml
i:{************************************}:e:\vs\humanvoice_bg\properties\publishprofiles\folderprofile.pubxml
++FolderProfile1.pubxml
i:{************************************}:e:\vs\humanvoice_bg\properties\publishprofiles\folderprofile1.pubxml
++Azure.AI.OpenAI (2.0.0-beta.2)
i:{************************************}:>3699
++Microsoft.CognitiveServices.Speech (1.42.0)
i:{************************************}:>3700
++Microsoft.VisualStudio.Azure.Containers.Tools.Targets (1.21.0)
i:{************************************}:>3698
++NAudio (2.2.1)
i:{************************************}:>3695
++NAudio.Lame.CrossPlatform (2.2.1)
i:{************************************}:>3696
++Newtonsoft.Json (13.0.3)
i:{************************************}:>3697
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\8.0.400\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\8.0.400\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{************************************}:>3693
