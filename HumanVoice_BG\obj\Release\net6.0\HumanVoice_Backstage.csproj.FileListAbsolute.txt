E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Model\en_2024_03_09\model.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Model\en_2024_03_09\tokens.txt
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Model\vad\silero_vad.onnx
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.exe
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.deps.json
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.runtimeconfig.json
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Azure.AI.OpenAI.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Azure.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\NAudio.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\NAudio.Asio.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\NAudio.Core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\NAudio.Midi.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\NAudio.Wasapi.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\NAudio.WinMM.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\Newtonsoft.Json.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\OpenAI.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\sherpa-onnx.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\System.ClientModel.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\System.Diagnostics.DiagnosticSource.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\System.Memory.Data.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libonnxruntime.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\onnxruntime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\onnxruntime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\onnxruntime.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoi.D2AE34CB.Up2Date
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\refint\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.pdb
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\obj\Release\net6.0\ref\HumanVoice_Backstage.dll
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\unity\YeHe_VoiceHuman\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Model\en_2024_03_09\model.onnx
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Model\en_2024_03_09\tokens.txt
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Model\vad\silero_vad.onnx
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.exe
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.deps.json
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.runtimeconfig.json
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\HumanVoice_Backstage.pdb
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Azure.AI.OpenAI.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Azure.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\NAudio.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\NAudio.Asio.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\NAudio.Core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\NAudio.Midi.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\NAudio.Wasapi.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\NAudio.WinMM.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\Newtonsoft.Json.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\OpenAI.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\sherpa-onnx.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\System.ClientModel.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\System.Diagnostics.DiagnosticSource.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\System.Memory.Data.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libpal_azure_c_shared.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libonnxruntime.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-arm64\native\libsherpa-onnx-c-api.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libonnxruntime.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\linux-x64\native\libsherpa-onnx-c-api.so
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libonnxruntime.1.17.1.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-arm64\native\libsherpa-onnx-c-api.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libonnxruntime.1.17.1.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\osx-x64\native\libsherpa-onnx-c-api.dylib
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\onnxruntime.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-arm64\native\sherpa-onnx-c-api.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\onnxruntime.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x64\native\sherpa-onnx-c-api.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\onnxruntime.dll
E:\unity\LianCeng_Human\HumanVoice_BG\bin\Release\net6.0\runtimes\win-x86\native\sherpa-onnx-c-api.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.csproj.AssemblyReference.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.GeneratedMSBuildEditorConfig.editorconfig
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.AssemblyInfoInputs.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.AssemblyInfo.cs
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.csproj.CoreCompileInputs.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoi.D2AE34CB.Up2Date
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\refint\HumanVoice_Backstage.dll
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.pdb
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\HumanVoice_Backstage.genruntimeconfig.cache
E:\unity\LianCeng_Human\HumanVoice_BG\obj\Release\net6.0\ref\HumanVoice_Backstage.dll
