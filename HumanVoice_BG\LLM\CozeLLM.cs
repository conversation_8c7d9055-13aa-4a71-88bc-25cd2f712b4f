﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace HumanVoice_Backstage.LLM
{
    internal class CozeLLM: LLMBase
    {
        public class UserDataStruct
        {
            public string bot_id { get; set; }

            public string user_id { get; set; }
            public List<Message> additional_messages { get; set; }

            public bool stream { get; set; }
            public bool auto_save_history { get; set; }

            /// <summary>
            /// 上一次会话
            /// </summary>
            [JsonIgnore]
            public string conversation_id { get; set; }
        }

        public class Message
        {
            private Message() { }

            public string role { get; set; }
            public string content { get; set; }

            public string content_type { get; set; }

            public string type { get; set; }


            public static Message CreateUserMessage(string content)
            {
                return new Message() { role = "user", content = content, content_type = "text" };
            }

            public static Message CreateAssistantMessage(string content)
            {
                return new Message() { role = "assistant", content = content, content_type = "text", type = "answer" };
            }
        }

        string conversation_id;

        UserDataStruct dataStruct;

        public override void  Init(string botid  )
        { 
            dataStruct = new UserDataStruct()
            {
                bot_id = botid,
                stream = true,
                additional_messages = new List<Message>(),
                user_id = DateTime.Now.ToBinary().ToString(),
                auto_save_history = true
            };

        }

        public override  async void RequestGPT(string prompt, Action<string, bool> callback)
        {

            try
            {
                var userMessage = Message.CreateUserMessage(prompt);
                dataStruct.additional_messages.Add(userMessage);


                var requestCoze = Newtonsoft.Json.JsonConvert.SerializeObject
                   (dataStruct);

                var request = HttpWebRequest.Create($"https://api.coze.cn/v3/chat?conversation_id={conversation_id}") as HttpWebRequest;
                request.Method = "POST";
                request.Headers.Add("Authorization", "Bearer pat_gMdScPW1FpapQQ1T4bM53yx6qyg4O0n2sMZqDKoOZT9nJThd6vC4sdwSXJxysdAm");
                request.ContentType = "application/json";

                using (var requestST = request.GetRequestStream())
                {
                    requestST.Write(Encoding.UTF8.GetBytes(requestCoze));
                }

                var k = request.GetResponse();
                using StreamReader resposneSTR = new StreamReader(k.GetResponseStream());
                string mess = "";
                while (!resposneSTR.EndOfStream)
                {
                    var l = resposneSTR.ReadLine(); 
                    if (l == "event:conversation.message.delta")
                    {
                        l = resposneSTR.ReadLine();

                        if (l.StartsWith("data:"))
                        {
                            var g = l.Replace("data:", "");

                            var tjson = JToken.Parse(g);

                            var content = tjson["content"]!.ToString();
                            mess += content;
                            callback.Invoke(content, false);

                        }
                    }
                    else if (l == "event:conversation.chat.completed")
                    {
                        l = resposneSTR.ReadLine();

                        if (l.StartsWith("data:"))
                        {
                            var g = l.Replace("data:", "");

                            var tjson = JToken.Parse(g);

                            conversation_id = tjson["conversation_id"].ToString();

                            break;
                        }
                    }
                }

                callback("", true);

                if (!string.IsNullOrEmpty(mess))
                {

                    dataStruct.additional_messages.Add(Message.CreateAssistantMessage(mess));
                }
                else
                {
                    dataStruct.additional_messages.Remove(userMessage);
                }
            }

            catch (Exception ex)
            {
                await Console.Out.WriteLineAsync(ex.ToString());
            }
            UpdateRemoveMessages();
        }


        private void UpdateRemoveMessages()
        {
            while (dataStruct.additional_messages.Count > 10)
            {
                dataStruct.additional_messages.RemoveAt(0);
            }
        }
    }
}
