{"format": 1, "restore": {"E:\\VS\\HumanVoice_BG\\HumanVoice_Backstage.csproj": {}}, "projects": {"E:\\VS\\HumanVoice_BG\\HumanVoice_Backstage.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\VS\\HumanVoice_BG\\HumanVoice_Backstage.csproj", "projectName": "HumanVoice_Backstage", "projectPath": "E:\\VS\\HumanVoice_BG\\HumanVoice_Backstage.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\VS\\HumanVoice_BG\\obj\\publish\\linux-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\vs\\gongxiang\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.AI.OpenAI": {"target": "Package", "version": "[2.0.0-beta.2, )"}, "Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.42.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.8, )", "autoReferenced": true}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}, "NAudio.Lame.CrossPlatform": {"target": "Package", "version": "[2.2.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}}