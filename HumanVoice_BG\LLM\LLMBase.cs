﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HumanVoice_Backstage.LLM
{
    public abstract class LLMBase
    {
        protected LLMBase(string systemMessage = null)
        {
            Init(systemMessage);
        }
        public abstract void Init(string systemMessage = null);

        public abstract void RequestGPT(string prompt, Action<string, bool> callback);
    }
}
