# HumanVoice 人声后台服务使用教程

## 目录
- [环境准备](#环境准备)
- [配置文件设置](#配置文件设置)
- [非Docker版本部署](#非docker版本部署)
- [Docker版本部署](#docker版本部署)
- [客户端连接示例](#客户端连接示例)
- [API接口说明](#api接口说明)
- [常见问题解决](#常见问题解决)

## 环境准备

### 系统要求
- **操作系统**: Windows 10/11, Linux, macOS
- **.NET Runtime**: .NET 8.0 或更高版本
- **内存**: 建议 4GB 以上
- **存储**: 至少 2GB 可用空间

### 必需依赖

#### 1. .NET 8.0 Runtime
```bash
# Windows (使用 winget)
winget install Microsoft.DotNet.Runtime.8

# Linux (Ubuntu/Debian)
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y dotnet-runtime-8.0

# macOS (使用 Homebrew)
brew install --cask dotnet
```

#### 2. FFmpeg
```bash
# Windows (使用 winget)
winget install Gyan.FFmpeg

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install ffmpeg

# macOS (使用 Homebrew)
brew install ffmpeg
```

#### 3. 验证安装
```bash
dotnet --version  # 应显示 8.0.x
ffmpeg -version   # 应显示 FFmpeg 版本信息
```

## 配置文件设置

### 1. 火山引擎TTS配置 (HuoShanTTSConfig.json)
```json
{
    "appid": "你的火山引擎AppID",
    "token": "你的火山引擎Token"
}
```

**获取方式**:
1. 访问 [火山引擎控制台](https://console.volcengine.com/)
2. 开通语音技术服务
3. 创建应用获取 AppID 和 Token

### 2. 豆包LLM配置 (DouBaoLLM.json)
```json
{
    "model": "ep-20241201234567-abcde",
    "key": "你的豆包API密钥"
}
```

### 3. 硅基VLM配置 (GuiJi.json)
```json
{
    "model": "Qwen/Qwen2.5-VL-32B-Instruct",
    "key": "你的硅基API密钥"
}
```

### 4. 音频文件准备
确保 `Audios` 目录包含以下结构：
```
Audios/
├── female/          # 女声音频样本
│   ├── 温柔女声.wav
│   ├── 甜美女声.wav
│   └── ...
└── male/            # 男声音频样本
    ├── 儒雅男声.wav
    ├── 磁性男声.wav
    └── ...
```

**音频要求**:
- 格式: WAV
- 采样率: 16kHz 或 44.1kHz
- 时长: 3-10秒
- 内容: 清晰的语音样本

## 非Docker版本部署

### 1. 下载项目
```bash
git clone <项目地址>
cd HumanVoice_BG
```

### 2. 配置文件设置
```bash
# 复制并编辑配置文件
cp Config/HuoShanTTSConfig.json.example Config/HuoShanTTSConfig.json
cp Config/DouBaoLLM.json.example Config/DouBaoLLM.json
cp Config/GuiJi.json.example Config/GuiJi.json

# 编辑配置文件，填入你的API密钥
nano Config/HuoShanTTSConfig.json
nano Config/DouBaoLLM.json
nano Config/GuiJi.json
```

### 3. 构建项目
```bash
# 还原依赖包
dotnet restore

# 构建项目
dotnet build --configuration Release

# 发布项目
dotnet publish --configuration Release --output ./publish
```

### 4. 运行服务
```bash
# 方式1: 直接运行
dotnet run

# 方式2: 运行发布版本
cd publish
dotnet HumanVoice_Backstage.dll

# 方式3: 后台运行 (Linux/macOS)
nohup dotnet HumanVoice_Backstage.dll > app.log 2>&1 &
```

### 5. 验证服务
```bash
# 检查服务是否启动
netstat -tlnp | grep 19465

# 或使用 curl 测试
curl -I http://localhost:19465
```

## Docker版本部署

### 1. 构建Docker镜像
```bash
# 进入项目目录
cd HumanVoice_BG

# 构建镜像
docker build -t humanvoice-backend .
```

### 2. 准备配置文件和数据
```bash
# 创建配置目录
mkdir -p ./docker-data/config
mkdir -p ./docker-data/audios

# 复制配置文件
cp Config/*.json ./docker-data/config/

# 复制音频文件
cp -r Audios/* ./docker-data/audios/
```

### 3. 运行容器
```bash
# 基本运行
docker run -d \
  --name humanvoice-backend \
  -p 19465:19465 \
  -v $(pwd)/docker-data/config:/app/Config \
  -v $(pwd)/docker-data/audios:/app/Audios \
  humanvoice-backend

# 带环境变量运行
docker run -d \
  --name humanvoice-backend \
  -p 19465:19465 \
  -e ASPNETCORE_ENVIRONMENT=Production \
  -v $(pwd)/docker-data/config:/app/Config \
  -v $(pwd)/docker-data/audios:/app/Audios \
  --restart unless-stopped \
  humanvoice-backend
```

### 4. Docker Compose 部署
创建 `docker-compose.yml`:
```yaml
version: '3.8'

services:
  humanvoice-backend:
    build: .
    container_name: humanvoice-backend
    ports:
      - "19465:19465"
    volumes:
      - ./docker-data/config:/app/Config
      - ./docker-data/audios:/app/Audios
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:19465"]
      interval: 30s
      timeout: 10s
      retries: 3
```

运行:
```bash
docker-compose up -d
```

### 5. 容器管理
```bash
# 查看日志
docker logs humanvoice-backend

# 进入容器
docker exec -it humanvoice-backend bash

# 停止容器
docker stop humanvoice-backend

# 重启容器
docker restart humanvoice-backend
```

## 客户端连接示例

### WebSocket连接地址
```
ws://localhost:19465/recognition
```

### 连接参数
- `isSendConfig=true`: 是否发送配置信息
- `isKeLong=true`: 使用克隆TTS
- `isFree=true`: 使用免费TTS
- `isLLMVoice=true`: 使用Coze TTS

### JavaScript客户端示例
```javascript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:19465/recognition?isSendConfig=true');

ws.onopen = function() {
    console.log('连接已建立');
    
    // 发送配置信息
    const config = {
        voiceType: "温柔女声",
        systemMessage: "你是一个友善的AI助手",
        botid: null,
        isvlm: null
    };
    ws.send(JSON.stringify(config));
};

ws.onmessage = function(event) {
    if (event.data instanceof Blob) {
        // 处理音频数据
        console.log('收到音频数据');
    } else {
        // 处理文本消息
        const data = JSON.parse(event.data);
        console.log('收到消息:', data);
    }
};

// 发送音频数据
function sendAudio(audioBuffer) {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(audioBuffer);
    }
}

// 发送文本消息
function sendText(text) {
    if (ws.readyState === WebSocket.OPEN) {
        ws.send(text);
    }
}
```

### Python客户端示例
```python
import asyncio
import websockets
import json
import base64

async def client():
    uri = "ws://localhost:19465/recognition?isSendConfig=true"
    
    async with websockets.connect(uri) as websocket:
        # 发送配置
        config = {
            "voiceType": "温柔女声",
            "systemMessage": "你是一个友善的AI助手",
            "botid": None,
            "isvlm": None
        }
        await websocket.send(json.dumps(config))
        
        # 发送文本消息
        await websocket.send("你好，请介绍一下自己")
        
        # 接收响应
        async for message in websocket:
            if isinstance(message, bytes):
                # 音频数据
                print(f"收到音频数据，长度: {len(message)}")
                # 保存音频文件
                with open("response.wav", "wb") as f:
                    f.write(message)
            else:
                # 文本消息
                data = json.loads(message)
                print(f"收到消息: {data}")

# 运行客户端
asyncio.run(client())
```

## API接口说明

### 消息类型

#### 1. 配置消息 (首次连接发送)
```json
{
    "voiceType": "温柔女声",
    "systemMessage": "你是一个友善的AI助手",
    "botid": "coze_bot_id",
    "isvlm": "true"
}
```

#### 2. 文本消息
直接发送文本字符串

#### 3. 音频消息
发送二进制音频数据 (WAV格式)

#### 4. 图像消息
发送带有特殊头部的二进制数据:
```
[0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09] + JPEG数据
```

### 响应消息格式

#### 1. 语音识别增量结果
```json
{
    "type": "voice_recognition_delta",
    "content": "识别中的文本..."
}
```

#### 2. LLM开始处理
```json
{
    "type": "start_llm",
    "content": "用户输入的文本"
}
```

#### 3. TTS音频数据
```json
{
    "type": "tts",
    "data": {
        "AudioData": "base64编码的音频数据",
        "Text": "对应的文本"
    }
}
```

#### 4. 处理完成
```json
{
    "type": "finish"
}
```

#### 5. 时间统计
```json
{
    "type": "invoke_time",
    "data": {
        "type": "request_llm_time",
        "timestamp": 1640995200000
    }
}
```

## 常见问题解决

### 1. 端口占用问题
```bash
# 查看端口占用
netstat -tlnp | grep 19465

# 杀死占用进程
sudo kill -9 <PID>

# 或修改代码中的端口号
```

### 2. FFmpeg未找到
```bash
# 确认FFmpeg在PATH中
which ffmpeg

# 如果不在PATH中，添加到环境变量
export PATH=$PATH:/path/to/ffmpeg/bin
```

### 3. 配置文件错误
- 检查JSON格式是否正确
- 确认API密钥是否有效
- 验证文件路径是否正确

### 4. 音频文件问题
- 确保音频文件格式为WAV
- 检查文件名是否包含特殊字符
- 验证音频文件是否损坏

### 5. 内存不足
```bash
# 监控内存使用
top -p $(pgrep -f HumanVoice)

# 调整Docker内存限制
docker run --memory=4g ...
```

### 6. 网络连接问题
- 检查防火墙设置
- 验证API服务是否可访问
- 确认网络代理配置

### 7. 日志查看
```bash
# 非Docker版本
tail -f app.log

# Docker版本
docker logs -f humanvoice-backend
```

## 性能优化建议

### 1. 系统配置
- 使用SSD存储
- 配置足够的内存
- 启用多核CPU支持

### 2. 网络优化
- 使用CDN加速API调用
- 配置连接池
- 启用HTTP/2

### 3. 缓存策略
- 缓存常用TTS结果
- 预加载音频样本
- 使用Redis缓存

## 监控和维护

### 1. 健康检查
```bash
# 检查服务状态
curl -f http://localhost:19465/health || echo "服务异常"

# 检查WebSocket连接
wscat -c ws://localhost:19465/recognition
```

### 2. 日志轮转
```bash
# 配置logrotate (Linux)
sudo nano /etc/logrotate.d/humanvoice
```

```
/path/to/app.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 app app
}
```

### 3. 自动重启脚本
```bash
#!/bin/bash
# restart.sh
while true; do
    if ! pgrep -f "HumanVoice_Backstage" > /dev/null; then
        echo "服务已停止，正在重启..."
        cd /path/to/app
        nohup dotnet HumanVoice_Backstage.dll > app.log 2>&1 &
    fi
    sleep 30
done
```

### 4. 系统服务配置 (Linux)
创建 `/etc/systemd/system/humanvoice.service`:
```ini
[Unit]
Description=HumanVoice Backend Service
After=network.target

[Service]
Type=simple
User=app
WorkingDirectory=/path/to/app
ExecStart=/usr/bin/dotnet HumanVoice_Backstage.dll
Restart=always
RestartSec=10
Environment=ASPNETCORE_ENVIRONMENT=Production

[Install]
WantedBy=multi-user.target
```

启用服务:
```bash
sudo systemctl enable humanvoice
sudo systemctl start humanvoice
sudo systemctl status humanvoice
```

## 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 19465/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=19465/tcp
sudo firewall-cmd --reload
```

### 2. SSL/TLS配置
如需HTTPS支持，可使用Nginx反向代理:
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:19465;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. API密钥管理
```bash
# 使用环境变量
export HUOSHAN_APPID="your_app_id"
export HUOSHAN_TOKEN="your_token"
export DOUBAO_KEY="your_key"
export GUIJI_KEY="your_key"

# 或使用.env文件
echo "HUOSHAN_APPID=your_app_id" > .env
echo "HUOSHAN_TOKEN=your_token" >> .env
```

## 扩展开发

### 1. 添加新的TTS服务
在 `TTS.cs` 中添加新的枚举值和实现方法:
```csharp
public enum TTSType
{
    HuoShan,
    keLong,
    Coze,
    Free,
    IndexTTSVLLM,
    YourNewTTS  // 新增
}

byte[] YourNewTTSMethod(string content)
{
    // 实现你的TTS逻辑
    return audioData;
}
```

### 2. 添加新的LLM服务
继承 `LLMBase` 类:
```csharp
public class YourLLM : LLMBase
{
    public override void Init(string systemMessage = null)
    {
        // 初始化逻辑
    }

    public override void RequestGPT(string prompt, Action<string, bool> callback)
    {
        // LLM请求逻辑
    }
}
```

### 3. 自定义音频处理
修改音频转换方法以支持更多格式:
```csharp
static byte[] ConvertAudioFormat(byte[] inputData, string inputFormat, string outputFormat)
{
    // 使用FFmpeg进行格式转换
}
```

## 故障排除指南

### 1. 服务无法启动
- 检查端口是否被占用
- 验证.NET运行时版本
- 查看详细错误日志

### 2. WebSocket连接失败
- 确认防火墙设置
- 检查代理配置
- 验证URL格式

### 3. TTS服务异常
- 检查API密钥有效性
- 验证网络连接
- 查看服务商状态页面

### 4. 音频质量问题
- 检查输入音频格式
- 调整采样率设置
- 验证FFmpeg版本

### 5. 内存泄漏
- 监控内存使用情况
- 检查音频缓冲区释放
- 优化大文件处理

---

**注意**:
1. 请确保所有API密钥的安全性，不要将其提交到版本控制系统中
2. 建议使用环境变量或加密配置文件来管理敏感信息
3. 定期备份配置文件和音频样本
4. 监控服务性能和资源使用情况
5. 及时更新依赖包以获得安全补丁
