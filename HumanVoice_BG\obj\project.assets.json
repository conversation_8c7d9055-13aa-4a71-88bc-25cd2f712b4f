{"version": 3, "targets": {"net8.0": {"Azure.AI.OpenAI/2.0.0-beta.2": {"type": "package", "dependencies": {"Azure.Core": "1.40.0", "OpenAI": "2.0.0-beta.5"}, "compile": {"lib/netstandard2.0/Azure.AI.OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.AI.OpenAI.dll": {"related": ".xml"}}}, "Azure.Core/1.40.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CognitiveServices.Speech/1.42.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"related": ".xml"}}, "build": {"build/Microsoft.CognitiveServices.Speech.props": {}}, "runtimeTargets": {"runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "ios-arm64"}, "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "iossimulator-arm64"}, "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "iossimulator-x64"}, "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libpal_azure_c_shared.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libpal_azure_c_shared_openssl3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libpal_azure_c_shared.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libpal_azure_c_shared_openssl3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libpal_azure_c_shared.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "osx-arm64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "osx-x64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"type": "package", "build": {"build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props": {}, "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "NAudio/2.2.1": {"type": "package", "dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinMM": "2.2.1"}, "compile": {"lib/net6.0/NAudio.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/NAudio.dll": {"related": ".xml"}}}, "NAudio.Asio/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}}, "NAudio.Core/2.2.1": {"type": "package", "compile": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}}, "NAudio.Lame.CrossPlatform/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/net8.0/LameDLLWrap.dll": {}, "lib/net8.0/NAudio.Lame.dll": {}}, "runtime": {"lib/net8.0/LameDLLWrap.dll": {}, "lib/net8.0/NAudio.Lame.dll": {}}, "contentFiles": {"contentFiles/any/net8.0/libmp3lame.32.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}, "contentFiles/any/net8.0/libmp3lame.64.dll": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}, "NAudio.Midi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}}, "NAudio.Wasapi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}}, "NAudio.WinMM/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "OpenAI/2.0.0-beta.5": {"type": "package", "dependencies": {"System.ClientModel": "1.1.0-beta.4"}, "compile": {"lib/net6.0/OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/OpenAI.dll": {"related": ".xml"}}}, "System.ClientModel/1.1.0-beta.4": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "compile": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}}}, "libraries": {"Azure.AI.OpenAI/2.0.0-beta.2": {"sha512": "Dgl1fXFggUZUyHgyyt4rEqbciG3FlZNmZzveT3NjH/r2U4vY0L9sPGs2LDgHjzwLzjXBRg+tBpDjkSeDfp/YLQ==", "type": "package", "path": "azure.ai.openai/2.0.0-beta.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.ai.openai.2.0.0-beta.2.nupkg.sha512", "azure.ai.openai.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.AI.OpenAI.dll", "lib/netstandard2.0/Azure.AI.OpenAI.xml"]}, "Azure.Core/1.40.0": {"sha512": "eOx6wk3kQ3SCnoAj7IytAu/d99l07PdarmUc+RmMkVOTkcQ3s+UQEaGzMyEqC2Ua4SKnOW4Xw/klLeB5V2PiSA==", "type": "package", "path": "azure.core/1.40.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.40.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"sha512": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CognitiveServices.Speech/1.42.0": {"sha512": "NNsLzj5qX2t7AS55rRWQGu4m7M4OSzMd1/5I8heVYhvcq18GyOX3baPGkGAMlhugvCoREXMlWCaH4SBzwZZ2mg==", "type": "package", "path": "microsoft.cognitiveservices.speech/1.42.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/Microsoft.CognitiveServices.Speech.props", "build/REDIST.txt", "build/Xamarin.iOS/Microsoft.CognitiveServices.Speech.targets", "build/Xamarin.iOS/libMicrosoft.CognitiveServices.Speech.core.a", "build/monoandroid/Microsoft.CognitiveServices.Speech.targets", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/native/ARM/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/Microsoft.CognitiveServices.Speech.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/include/c_api/CMakeLists.txt", "build/native/include/c_api/azac_api_c_common.h", "build/native/include/c_api/azac_api_c_diagnostics.h", "build/native/include/c_api/azac_api_c_error.h", "build/native/include/c_api/azac_api_c_pal.h", "build/native/include/c_api/azac_debug.h", "build/native/include/c_api/azac_error.h", "build/native/include/c_api/speechapi_c.h", "build/native/include/c_api/speechapi_c_audio_config.h", "build/native/include/c_api/speechapi_c_audio_processing_options.h", "build/native/include/c_api/speechapi_c_audio_stream.h", "build/native/include/c_api/speechapi_c_audio_stream_format.h", "build/native/include/c_api/speechapi_c_auto_detect_source_lang_config.h", "build/native/include/c_api/speechapi_c_common.h", "build/native/include/c_api/speechapi_c_connection.h", "build/native/include/c_api/speechapi_c_conversation.h", "build/native/include/c_api/speechapi_c_conversation_transcription_result.h", "build/native/include/c_api/speechapi_c_conversation_translator.h", "build/native/include/c_api/speechapi_c_diagnostics.h", "build/native/include/c_api/speechapi_c_dialog_service_config.h", "build/native/include/c_api/speechapi_c_dialog_service_connector.h", "build/native/include/c_api/speechapi_c_embedded_speech_config.h", "build/native/include/c_api/speechapi_c_error.h", "build/native/include/c_api/speechapi_c_ext_audiocompression.h", "build/native/include/c_api/speechapi_c_factory.h", "build/native/include/c_api/speechapi_c_grammar.h", "build/native/include/c_api/speechapi_c_hybrid_speech_config.h", "build/native/include/c_api/speechapi_c_intent_recognizer.h", "build/native/include/c_api/speechapi_c_intent_result.h", "build/native/include/c_api/speechapi_c_intent_trigger.h", "build/native/include/c_api/speechapi_c_json.h", "build/native/include/c_api/speechapi_c_keyword_recognition_model.h", "build/native/include/c_api/speechapi_c_language_understanding_model.h", "build/native/include/c_api/speechapi_c_meeting.h", "build/native/include/c_api/speechapi_c_meeting_transcription_result.h", "build/native/include/c_api/speechapi_c_operations.h", "build/native/include/c_api/speechapi_c_participant.h", "build/native/include/c_api/speechapi_c_pattern_matching_model.h", "build/native/include/c_api/speechapi_c_pronunciation_assessment_config.h", "build/native/include/c_api/speechapi_c_property_bag.h", "build/native/include/c_api/speechapi_c_recognizer.h", "build/native/include/c_api/speechapi_c_result.h", "build/native/include/c_api/speechapi_c_session.h", "build/native/include/c_api/speechapi_c_source_lang_config.h", "build/native/include/c_api/speechapi_c_speaker_recognition.h", "build/native/include/c_api/speechapi_c_speech_config.h", "build/native/include/c_api/speechapi_c_speech_recognition_model.h", "build/native/include/c_api/speechapi_c_speech_translation_config.h", "build/native/include/c_api/speechapi_c_speech_translation_model.h", "build/native/include/c_api/speechapi_c_synthesis_request.h", "build/native/include/c_api/speechapi_c_synthesizer.h", "build/native/include/c_api/speechapi_c_translation_recognizer.h", "build/native/include/c_api/speechapi_c_translation_result.h", "build/native/include/c_api/speechapi_c_user.h", "build/native/include/c_api/spxdebug.h", "build/native/include/c_api/spxerror.h", "build/native/include/cxx_api/CMakeLists.txt", "build/native/include/cxx_api/azac_api_cxx_common.h", "build/native/include/cxx_api/speechapi_cxx.h", "build/native/include/cxx_api/speechapi_cxx_audio_config.h", "build/native/include/cxx_api/speechapi_cxx_audio_data_stream.h", "build/native/include/cxx_api/speechapi_cxx_audio_processing_options.h", "build/native/include/cxx_api/speechapi_cxx_audio_stream.h", "build/native/include/cxx_api/speechapi_cxx_audio_stream_format.h", "build/native/include/cxx_api/speechapi_cxx_auto_detect_source_lang_config.h", "build/native/include/cxx_api/speechapi_cxx_auto_detect_source_lang_result.h", "build/native/include/cxx_api/speechapi_cxx_class_language_model.h", "build/native/include/cxx_api/speechapi_cxx_common.h", "build/native/include/cxx_api/speechapi_cxx_connection.h", "build/native/include/cxx_api/speechapi_cxx_connection_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_connection_message.h", "build/native/include/cxx_api/speechapi_cxx_connection_message_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_conversation.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcriber.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcription_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcription_result.h", "build/native/include/cxx_api/speechapi_cxx_conversation_translator.h", "build/native/include/cxx_api/speechapi_cxx_conversation_translator_events.h", "build/native/include/cxx_api/speechapi_cxx_conversational_language_understanding_model.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_config.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_connector.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_connector_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_embedded_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_enums.h", "build/native/include/cxx_api/speechapi_cxx_event_logger.h", "build/native/include/cxx_api/speechapi_cxx_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_eventsignal.h", "build/native/include/cxx_api/speechapi_cxx_eventsignalbase.h", "build/native/include/cxx_api/speechapi_cxx_file_logger.h", "build/native/include/cxx_api/speechapi_cxx_grammar.h", "build/native/include/cxx_api/speechapi_cxx_grammar_list.h", "build/native/include/cxx_api/speechapi_cxx_grammar_phrase.h", "build/native/include/cxx_api/speechapi_cxx_hybrid_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_intent_trigger.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_model.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_language_understanding_model.h", "build/native/include/cxx_api/speechapi_cxx_log_level.h", "build/native/include/cxx_api/speechapi_cxx_meeting.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcriber.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcription_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcription_result.h", "build/native/include/cxx_api/speechapi_cxx_memory_logger.h", "build/native/include/cxx_api/speechapi_cxx_participant.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_entity.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_intent.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_model.h", "build/native/include/cxx_api/speechapi_cxx_phrase_list_grammar.h", "build/native/include/cxx_api/speechapi_cxx_pronunciation_assessment_config.h", "build/native/include/cxx_api/speechapi_cxx_pronunciation_assessment_result.h", "build/native/include/cxx_api/speechapi_cxx_properties.h", "build/native/include/cxx_api/speechapi_cxx_recognition_async_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_recognition_base_async_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_session.h", "build/native/include/cxx_api/speechapi_cxx_session_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_smart_handle.h", "build/native/include/cxx_api/speechapi_cxx_source_lang_config.h", "build/native/include/cxx_api/speechapi_cxx_source_language_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speaker_identification_model.h", "build/native/include/cxx_api/speechapi_cxx_speaker_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_speaker_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speaker_verification_model.h", "build/native/include/cxx_api/speechapi_cxx_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_model.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_bookmark_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_request.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_result.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_viseme_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_word_boundary_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesizer.h", "build/native/include/cxx_api/speechapi_cxx_speech_translation_config.h", "build/native/include/cxx_api/speechapi_cxx_speech_translation_model.h", "build/native/include/cxx_api/speechapi_cxx_string_helpers.h", "build/native/include/cxx_api/speechapi_cxx_synthesis_voices_result.h", "build/native/include/cxx_api/speechapi_cxx_translation_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_translation_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_translation_result.h", "build/native/include/cxx_api/speechapi_cxx_user.h", "build/native/include/cxx_api/speechapi_cxx_utils.h", "build/native/include/cxx_api/speechapi_cxx_voice_info.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_client.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_enrollment_result.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_phrase_result.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_result.h", "build/native/uap/ARM/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/net462/Microsoft.CognitiveServices.Speech.targets", "build/net8.0-android/Microsoft.CognitiveServices.Speech.targets", "build/net8.0-ios/Microsoft.CognitiveServices.Speech.targets", "lib/Xamarin.iOS/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/Xamarin.iOS/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/monoandroid/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/monoandroid/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net462/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net462/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net8.0-android/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net8.0-android/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net8.0-ios/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net8.0-ios/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net8.0-maccatalyst/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net8.0-maccatalyst/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/uap10.0/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/uap10.0/Microsoft.CognitiveServices.Speech.csharp.xml", "microsoft.cognitiveservices.speech.1.42.0.nupkg.sha512", "microsoft.cognitiveservices.speech.nuspec", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-arm/native/libpal_azure_c_shared.so", "runtimes/linux-arm/native/libpal_azure_c_shared_openssl3.so", "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-arm64/native/libpal_azure_c_shared.so", "runtimes/linux-arm64/native/libpal_azure_c_shared_openssl3.so", "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-x64/native/libpal_azure_c_shared.so", "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so", "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib", "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"sha512": "8NudeHOE56YsY59HYY89akRMup8Ho+7Y3cADTGjajjWroXVU9RQai2nA6PfteB8AuzmRHZ5NZQB2BnWhQEul5g==", "type": "package", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "EULA.md", "ThirdPartyNotices.txt", "build/Container.props", "build/Container.targets", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets", "build/Rules/GeneralBrowseObject.xaml", "build/Rules/cs-CZ/GeneralBrowseObject.xaml", "build/Rules/de-DE/GeneralBrowseObject.xaml", "build/Rules/es-ES/GeneralBrowseObject.xaml", "build/Rules/fr-FR/GeneralBrowseObject.xaml", "build/Rules/it-IT/GeneralBrowseObject.xaml", "build/Rules/ja-JP/GeneralBrowseObject.xaml", "build/Rules/ko-KR/GeneralBrowseObject.xaml", "build/Rules/pl-PL/GeneralBrowseObject.xaml", "build/Rules/pt-BR/GeneralBrowseObject.xaml", "build/Rules/ru-RU/GeneralBrowseObject.xaml", "build/Rules/tr-TR/GeneralBrowseObject.xaml", "build/Rules/zh-CN/GeneralBrowseObject.xaml", "build/Rules/zh-TW/GeneralBrowseObject.xaml", "build/ToolsTarget.props", "build/ToolsTarget.targets", "icon.png", "microsoft.visualstudio.azure.containers.tools.targets.1.21.0.nupkg.sha512", "microsoft.visualstudio.azure.containers.tools.targets.nuspec", "tools/Microsoft.VisualStudio.Containers.Tools.Common.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Shared.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Tasks.dll", "tools/Newtonsoft.Json.dll", "tools/System.Security.Principal.Windows.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "NAudio/2.2.1": {"sha512": "c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "type": "package", "path": "naudio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.dll", "lib/net472/NAudio.xml", "lib/net6.0-windows7.0/NAudio.dll", "lib/net6.0-windows7.0/NAudio.xml", "lib/net6.0/NAudio.dll", "lib/net6.0/NAudio.xml", "lib/netcoreapp3.1/NAudio.dll", "lib/netcoreapp3.1/NAudio.xml", "license.txt", "naudio-icon.png", "naudio.2.2.1.nupkg.sha512", "naudio.nuspec"]}, "NAudio.Asio/2.2.1": {"sha512": "hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "type": "package", "path": "naudio.asio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Asio.dll", "lib/netstandard2.0/NAudio.Asio.xml", "naudio-icon.png", "naudio.asio.2.2.1.nupkg.sha512", "naudio.asio.nuspec"]}, "NAudio.Core/2.2.1": {"sha512": "GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "type": "package", "path": "naudio.core/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Core.dll", "lib/netstandard2.0/NAudio.Core.xml", "naudio-icon.png", "naudio.core.2.2.1.nupkg.sha512", "naudio.core.nuspec"]}, "NAudio.Lame.CrossPlatform/2.2.1": {"sha512": "imx7K8YYc3LYB3EuUqbMaqUWn5W1IpNOxUWertNGZY1wlkaDhJdDxNnDappkvUHffLLMcffsoa5g+kntpbc38Q==", "type": "package", "path": "naudio.lame.crossplatform/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "content/libmp3lame.32.dll", "content/libmp3lame.64.dll", "contentFiles/any/net8.0/libmp3lame.32.dll", "contentFiles/any/net8.0/libmp3lame.64.dll", "lib/net8.0/LameDLLWrap.dll", "lib/net8.0/NAudio.Lame.dll", "naudio.lame.crossplatform.2.2.1.nupkg.sha512", "naudio.lame.crossplatform.nuspec"]}, "NAudio.Midi/2.2.1": {"sha512": "6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "type": "package", "path": "naudio.midi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Midi.dll", "lib/netstandard2.0/NAudio.Midi.xml", "naudio-icon.png", "naudio.midi.2.2.1.nupkg.sha512", "naudio.midi.nuspec"]}, "NAudio.Wasapi/2.2.1": {"sha512": "lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "type": "package", "path": "naudio.wasapi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Wasapi.dll", "lib/netstandard2.0/NAudio.Wasapi.xml", "lib/uap10.0.18362/NAudio.Wasapi.dll", "lib/uap10.0.18362/NAudio.Wasapi.pri", "lib/uap10.0.18362/NAudio.Wasapi.xml", "naudio-icon.png", "naudio.wasapi.2.2.1.nupkg.sha512", "naudio.wasapi.nuspec"]}, "NAudio.WinMM/2.2.1": {"sha512": "xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "type": "package", "path": "naudio.winmm/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.WinMM.dll", "lib/netstandard2.0/NAudio.WinMM.xml", "naudio-icon.png", "naudio.winmm.2.2.1.nupkg.sha512", "naudio.winmm.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "OpenAI/2.0.0-beta.5": {"sha512": "1s9Myw7G50qQ8LXhgsMefgbpVVEqXhUJ0u1qGRFYiysw/ycwHiIBluDOHhQHEOwBADny0QeadQ7NpcrrXRXt2g==", "type": "package", "path": "openai/2.0.0-beta.5", "files": [".nupkg.metadata", ".signature.p7s", "OpenAI.png", "README.md", "lib/net6.0/OpenAI.dll", "lib/net6.0/OpenAI.xml", "lib/netstandard2.0/OpenAI.dll", "lib/netstandard2.0/OpenAI.xml", "openai.2.0.0-beta.5.nupkg.sha512", "openai.nuspec"]}, "System.ClientModel/1.1.0-beta.4": {"sha512": "TSzxsr0lU6ohBsSWXoJtLaI+Jl739f6I2eEAmAxTERxRmXs49kp/FwsUuXhjZ95Y7TcdbUi6Peb79TZhC/cBvg==", "type": "package", "path": "system.clientmodel/1.1.0-beta.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.1.0-beta.4.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/4.7.2": {"sha512": "iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "type": "package", "path": "system.text.encodings.web/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.7.2.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net8.0": ["Azure.AI.OpenAI >= 2.0.0-beta.2", "Microsoft.CognitiveServices.Speech >= 1.42.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.21.0", "NAudio >= 2.2.1", "NAudio.Lame.CrossPlatform >= 2.2.1", "Newtonsoft.Json >= 13.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\soft\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\P1022\\HumanVoice_BG\\HumanVoice_Backstage.csproj", "projectName": "HumanVoice_Backstage", "projectPath": "C:\\Users\\<USER>\\Desktop\\P1022\\HumanVoice_BG\\HumanVoice_Backstage.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\P1022\\HumanVoice_BG\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\soft\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.AI.OpenAI": {"target": "Package", "version": "[2.0.0-beta.2, )"}, "Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.42.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.0, )"}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}, "NAudio.Lame.CrossPlatform": {"target": "Package", "version": "[2.2.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}