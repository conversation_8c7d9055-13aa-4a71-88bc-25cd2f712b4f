{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/linux-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/linux-x64": {"HumanVoice_Backstage/1.0.0": {"dependencies": {"Azure.AI.OpenAI": "2.0.0-beta.2", "Microsoft.CognitiveServices.Speech": "1.42.0", "Microsoft.NET.ILLink.Tasks": "8.0.8", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "NAudio": "2.2.1", "NAudio.Lame.CrossPlatform": "2.2.1", "Newtonsoft.Json": "13.0.3", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "8.0.8"}, "runtime": {"HumanVoice_Backstage.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.8": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.824.36612"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "8.0.824.36612"}}}, "Azure.AI.OpenAI/2.0.0-beta.2": {"dependencies": {"Azure.Core": "1.40.0", "OpenAI": "2.0.0-beta.5"}, "runtime": {"lib/netstandard2.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.24.31402"}}}, "Azure.Core/1.40.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.1.0-beta.4", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.40.0.0", "fileVersion": "1.4000.24.30605"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CognitiveServices.Speech/1.42.0": {"runtime": {"runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assemblyVersion": "1.42.0.28", "fileVersion": "1.42.0.28"}}, "native": {"runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpal_azure_c_shared.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so": {"fileVersion": "0.0.0.0"}}}, "Microsoft.NET.ILLink.Tasks/8.0.8": {}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "NAudio/2.2.1": {"dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinMM": "2.2.1"}, "runtime": {"lib/net6.0/NAudio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Asio/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Core/2.2.1": {"runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Lame.CrossPlatform/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/net8.0/LameDLLWrap.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/NAudio.Lame.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Midi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Wasapi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.WinMM/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "OpenAI/2.0.0-beta.5": {"dependencies": {"System.ClientModel": "1.1.0-beta.4"}, "runtime": {"lib/net6.0/OpenAI.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "System.ClientModel/1.1.0-beta.4": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.26606"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"HumanVoice_Backstage/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Azure.AI.OpenAI/2.0.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-Dgl1fXFggUZUyHgyyt4rEqbciG3FlZNmZzveT3NjH/r2U4vY0L9sPGs2LDgHjzwLzjXBRg+tBpDjkSeDfp/YLQ==", "path": "azure.ai.openai/2.0.0-beta.2", "hashPath": "azure.ai.openai.2.0.0-beta.2.nupkg.sha512"}, "Azure.Core/1.40.0": {"type": "package", "serviceable": true, "sha512": "sha512-eOx6wk3kQ3SCnoAj7IytAu/d99l07PdarmUc+RmMkVOTkcQ3s+UQEaGzMyEqC2Ua4SKnOW4Xw/klLeB5V2PiSA==", "path": "azure.core/1.40.0", "hashPath": "azure.core.1.40.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CognitiveServices.Speech/1.42.0": {"type": "package", "serviceable": true, "sha512": "sha512-NNsLzj5qX2t7AS55rRWQGu4m7M4OSzMd1/5I8heVYhvcq18GyOX3baPGkGAMlhugvCoREXMlWCaH4SBzwZZ2mg==", "path": "microsoft.cognitiveservices.speech/1.42.0", "hashPath": "microsoft.cognitiveservices.speech.1.42.0.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/8.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-P8wR6MUWwYXIjPJuBaZgo5zlI/GWI6QEAo6NyVIbPefa9CCkohYu7dP2rD/mrqnjEqfRHyl+h9VZrDoGpELqYg==", "path": "microsoft.net.illink.tasks/8.0.8", "hashPath": "microsoft.net.illink.tasks.8.0.8.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-8N<PERSON>HOE56YsY59HYY89akRMup8Ho+7Y3cADTGjajjWroXVU9RQai2nA6PfteB8AuzmRHZ5NZQB2BnWhQEul5g==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.0", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.21.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "NAudio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "path": "naudio/2.2.1", "hashPath": "naudio.2.2.1.nupkg.sha512"}, "NAudio.Asio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "path": "naudio.asio/2.2.1", "hashPath": "naudio.asio.2.2.1.nupkg.sha512"}, "NAudio.Core/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "path": "naudio.core/2.2.1", "hashPath": "naudio.core.2.2.1.nupkg.sha512"}, "NAudio.Lame.CrossPlatform/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-imx7K8YYc3LYB3EuUqbMaqUWn5W1IpNOxUWertNGZY1wlkaDhJdDxNnDappkvUHffLLMcffsoa5g+kntpbc38Q==", "path": "naudio.lame.crossplatform/2.2.1", "hashPath": "naudio.lame.crossplatform.2.2.1.nupkg.sha512"}, "NAudio.Midi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "path": "naudio.midi/2.2.1", "hashPath": "naudio.midi.2.2.1.nupkg.sha512"}, "NAudio.Wasapi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "path": "naudio.wasapi/2.2.1", "hashPath": "naudio.wasapi.2.2.1.nupkg.sha512"}, "NAudio.WinMM/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "path": "naudio.winmm/2.2.1", "hashPath": "naudio.winmm.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenAI/2.0.0-beta.5": {"type": "package", "serviceable": true, "sha512": "sha512-1s9Myw7G50qQ8LXhgsMefgbpVVEqXhUJ0u1qGRFYiysw/ycwHiIBluDOHhQHEOwBADny0QeadQ7NpcrrXRXt2g==", "path": "openai/2.0.0-beta.5", "hashPath": "openai.2.0.0-beta.5.nupkg.sha512"}, "System.ClientModel/1.1.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-TSzxsr0lU6ohBsSWXoJtLaI+Jl739f6I2eEAmAxTERxRmXs49kp/FwsUuXhjZ95Y7TcdbUi6Peb79TZhC/cBvg==", "path": "system.clientmodel/1.1.0-beta.4", "hashPath": "system.clientmodel.1.1.0-beta.4.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}, "runtimes": {"android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"]}}