{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/linux-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/linux-x64": {"HumanVoice_Backstage/1.0.0": {"dependencies": {"Azure.AI.OpenAI": "2.0.0-beta.2", "Microsoft.CognitiveServices.Speech": "1.42.0", "Microsoft.NET.ILLink.Analyzers": "7.0.100-1.23401.1", "Microsoft.NET.ILLink.Tasks": "7.0.100-1.23401.1", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "NAudio": "2.2.1", "Newtonsoft.Json": "13.0.3", "org.k2fsa.sherpa.onnx": "1.10.22", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "6.0.33"}, "runtime": {"HumanVoice_Backstage.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/6.0.33": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.100.3324.36610"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "6.0.3324.36610"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3324.36610"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.3324.36610"}}}, "Azure.AI.OpenAI/2.0.0-beta.2": {"dependencies": {"Azure.Core": "1.40.0", "OpenAI": "2.0.0-beta.5"}, "runtime": {"lib/netstandard2.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.24.31402"}}}, "Azure.Core/1.40.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.1.0-beta.4", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.40.0.0", "fileVersion": "1.4000.24.30605"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CognitiveServices.Speech/1.42.0": {"runtime": {"runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assemblyVersion": "1.42.0.28", "fileVersion": "1.42.0.28"}}, "native": {"runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpal_azure_c_shared.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so": {"fileVersion": "0.0.0.0"}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23401.1": {}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23401.1": {}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "NAudio/2.2.1": {"dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinMM": "2.2.1"}, "runtime": {"lib/net6.0/NAudio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Asio/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Core/2.2.1": {"runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Midi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Wasapi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.WinMM/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "OpenAI/2.0.0-beta.5": {"dependencies": {"System.ClientModel": "1.1.0-beta.4"}, "runtime": {"lib/net6.0/OpenAI.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "org.k2fsa.sherpa.onnx/1.10.22": {"dependencies": {"org.k2fsa.sherpa.onnx.runtime.linux-arm64": "1.10.22", "org.k2fsa.sherpa.onnx.runtime.linux-x64": "1.10.22", "org.k2fsa.sherpa.onnx.runtime.osx-arm64": "1.10.22", "org.k2fsa.sherpa.onnx.runtime.osx-x64": "1.10.22", "org.k2fsa.sherpa.onnx.runtime.win-arm64": "1.10.22", "org.k2fsa.sherpa.onnx.runtime.win-x64": "1.10.22", "org.k2fsa.sherpa.onnx.runtime.win-x86": "1.10.22"}, "runtime": {"lib/net6.0/sherpa-onnx.dll": {"assemblyVersion": "1.10.22.0", "fileVersion": "1.10.22.0"}}}, "org.k2fsa.sherpa.onnx.runtime.linux-arm64/1.10.22": {}, "org.k2fsa.sherpa.onnx.runtime.linux-x64/1.10.22": {"native": {"runtimes/linux-x64/native/libonnxruntime.so": {"fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libsherpa-onnx-c-api.so": {"fileVersion": "0.0.0.0"}}}, "org.k2fsa.sherpa.onnx.runtime.osx-arm64/1.10.22": {}, "org.k2fsa.sherpa.onnx.runtime.osx-x64/1.10.22": {}, "org.k2fsa.sherpa.onnx.runtime.win-arm64/1.10.22": {}, "org.k2fsa.sherpa.onnx.runtime.win-x64/1.10.22": {}, "org.k2fsa.sherpa.onnx.runtime.win-x86/1.10.22": {}, "System.ClientModel/1.1.0-beta.4": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.26606"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"HumanVoice_Backstage/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/6.0.33": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Azure.AI.OpenAI/2.0.0-beta.2": {"type": "package", "serviceable": true, "sha512": "sha512-Dgl1fXFggUZUyHgyyt4rEqbciG3FlZNmZzveT3NjH/r2U4vY0L9sPGs2LDgHjzwLzjXBRg+tBpDjkSeDfp/YLQ==", "path": "azure.ai.openai/2.0.0-beta.2", "hashPath": "azure.ai.openai.2.0.0-beta.2.nupkg.sha512"}, "Azure.Core/1.40.0": {"type": "package", "serviceable": true, "sha512": "sha512-eOx6wk3kQ3SCnoAj7IytAu/d99l07PdarmUc+RmMkVOTkcQ3s+UQEaGzMyEqC2Ua4SKnOW4Xw/klLeB5V2PiSA==", "path": "azure.core/1.40.0", "hashPath": "azure.core.1.40.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CognitiveServices.Speech/1.42.0": {"type": "package", "serviceable": true, "sha512": "sha512-NNsLzj5qX2t7AS55rRWQGu4m7M4OSzMd1/5I8heVYhvcq18GyOX3baPGkGAMlhugvCoREXMlWCaH4SBzwZZ2mg==", "path": "microsoft.cognitiveservices.speech/1.42.0", "hashPath": "microsoft.cognitiveservices.speech.1.42.0.nupkg.sha512"}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23401.1": {"type": "package", "serviceable": true, "sha512": "sha512-XirkjOLc5Vc3HsXRc2Z6ZbQv6l0RvWgJa/31w7XqZ914MoSi3H3OCNRMWFw7H2EYfsnKbokFfhCcysAmUcEOgw==", "path": "microsoft.net.illink.analyzers/7.0.100-1.23401.1", "hashPath": "microsoft.net.illink.analyzers.7.0.100-1.23401.1.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23401.1": {"type": "package", "serviceable": true, "sha512": "sha512-mI6vCdPEhluLtMn/GV0texEWg5oAPQWCCE4LWspM+Bmy75Nd4EQsziQXrdOFqNeSBQMrxDX9C/O5Xi3kpKSMIw==", "path": "microsoft.net.illink.tasks/7.0.100-1.23401.1", "hashPath": "microsoft.net.illink.tasks.7.0.100-1.23401.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-8N<PERSON>HOE56YsY59HYY89akRMup8Ho+7Y3cADTGjajjWroXVU9RQai2nA6PfteB8AuzmRHZ5NZQB2BnWhQEul5g==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.21.0", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.21.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "NAudio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "path": "naudio/2.2.1", "hashPath": "naudio.2.2.1.nupkg.sha512"}, "NAudio.Asio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "path": "naudio.asio/2.2.1", "hashPath": "naudio.asio.2.2.1.nupkg.sha512"}, "NAudio.Core/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "path": "naudio.core/2.2.1", "hashPath": "naudio.core.2.2.1.nupkg.sha512"}, "NAudio.Midi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "path": "naudio.midi/2.2.1", "hashPath": "naudio.midi.2.2.1.nupkg.sha512"}, "NAudio.Wasapi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "path": "naudio.wasapi/2.2.1", "hashPath": "naudio.wasapi.2.2.1.nupkg.sha512"}, "NAudio.WinMM/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "path": "naudio.winmm/2.2.1", "hashPath": "naudio.winmm.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenAI/2.0.0-beta.5": {"type": "package", "serviceable": true, "sha512": "sha512-1s9Myw7G50qQ8LXhgsMefgbpVVEqXhUJ0u1qGRFYiysw/ycwHiIBluDOHhQHEOwBADny0QeadQ7NpcrrXRXt2g==", "path": "openai/2.0.0-beta.5", "hashPath": "openai.2.0.0-beta.5.nupkg.sha512"}, "org.k2fsa.sherpa.onnx/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-ecl9nZQ7SwB68qMaEiXbsMKVw8YgUxwosNIvP2NXyJAr0SFJFRKzmnE63igHVJahxdkyhKeddk6IB4HWFPz+iQ==", "path": "org.k2fsa.sherpa.onnx/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.linux-arm64/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-v81qd0BJ/VCay23rmLslvRWGU3Q+IRmT6VZj6cIZ0J5iIaNfJLpYTM0uuXV+7795KuqtHgKnBnaTPX4ZisYXjw==", "path": "org.k2fsa.sherpa.onnx.runtime.linux-arm64/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.linux-arm64.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.linux-x64/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-C70m2njFocYc+oJb4WYCetB5Qt1tuHLjTxhQlyBx+CI+j/CTSodFCAgk/7AzYh3Zxu/6G+XFua8t+7BSvXsQzw==", "path": "org.k2fsa.sherpa.onnx.runtime.linux-x64/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.linux-x64.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.osx-arm64/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-2JKgz6SuSGLlPdZlDmISXaTwal+/tpRDP/b/9LNzNRBTAPESrNiz3QUmEol9gWyzt65DOy9QG6374PUwzTeq4A==", "path": "org.k2fsa.sherpa.onnx.runtime.osx-arm64/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.osx-arm64.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.osx-x64/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-EETQ3i+Uada+fCnZIcaW1qsOhIDlCpe+QFAJLyeD+Z94muD6FHQFesssRR2t5uIynNZBF+2LBfnU6V5M09YxfQ==", "path": "org.k2fsa.sherpa.onnx.runtime.osx-x64/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.osx-x64.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.win-arm64/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-b9xXecqm3Vi292qpbpDsy7fDBgw9/Z8mlAVjbTftI1FrUEmG2TaFtWP2suZ6t0+Vfc6MsgVB6lA440FMecOARA==", "path": "org.k2fsa.sherpa.onnx.runtime.win-arm64/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.win-arm64.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.win-x64/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-bBs+prvoZJjgBwHMeo/99Hng3NRbp77VxT6/tMhv+GwdqxjzVdAdpb8py8rHi6E0szo8p0yR9PP9nIxAfnOfyA==", "path": "org.k2fsa.sherpa.onnx.runtime.win-x64/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.win-x64.1.10.22.nupkg.sha512"}, "org.k2fsa.sherpa.onnx.runtime.win-x86/1.10.22": {"type": "package", "serviceable": true, "sha512": "sha512-BGI0Z4/NPoxjEKYQHmWwd8RNijC7NDBmLu6svKXeBL65jMgUaHIg4brx/aAjd62Jy6y3JHT8y9UuAcKhnbpQMg==", "path": "org.k2fsa.sherpa.onnx.runtime.win-x86/1.10.22", "hashPath": "org.k2fsa.sherpa.onnx.runtime.win-x86.1.10.22.nupkg.sha512"}, "System.ClientModel/1.1.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-TSzxsr0lU6ohBsSWXoJtLaI+Jl739f6I2eEAmAxTERxRmXs49kp/FwsUuXhjZ95Y7TcdbUi6Peb79TZhC/cBvg==", "path": "system.clientmodel/1.1.0-beta.4", "hashPath": "system.clientmodel.1.1.0-beta.4.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}, "runtimes": {"alpine-x64": ["alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.10-x64": ["alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.11-x64": ["alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.12-x64": ["alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.13-x64": ["alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.14-x64": ["alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.15-x64": ["alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.16-x64": ["alpine.3.16", "alpine.3.15-x64", "alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.17-x64": ["alpine.3.17", "alpine.3.16-x64", "alpine.3.16", "alpine.3.15-x64", "alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.18-x64": ["alpine.3.18", "alpine.3.17-x64", "alpine.3.17", "alpine.3.16-x64", "alpine.3.16", "alpine.3.15-x64", "alpine.3.15", "alpine.3.14-x64", "alpine.3.14", "alpine.3.13-x64", "alpine.3.13", "alpine.3.12-x64", "alpine.3.12", "alpine.3.11-x64", "alpine.3.11", "alpine.3.10-x64", "alpine.3.10", "alpine.3.9-x64", "alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.6-x64": ["alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.7-x64": ["alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.8-x64": ["alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "alpine.3.9-x64": ["alpine.3.9", "alpine.3.8-x64", "alpine.3.8", "alpine.3.7-x64", "alpine.3.7", "alpine.3.6-x64", "alpine.3.6", "alpine-x64", "alpine", "linux-musl-x64", "linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.21-x64": ["android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.22-x64": ["android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.23-x64": ["android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.24-x64": ["android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.25-x64": ["android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.26-x64": ["android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.27-x64": ["android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.28-x64": ["android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.29-x64": ["android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.30-x64": ["android.30", "android.29-x64", "android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.31-x64": ["android.31", "android.30-x64", "android.30", "android.29-x64", "android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "android.32-x64": ["android.32", "android.31-x64", "android.31", "android.30-x64", "android.30", "android.29-x64", "android.29", "android.28-x64", "android.28", "android.27-x64", "android.27", "android.26-x64", "android.26", "android.25-x64", "android.25", "android.24-x64", "android.24", "android.23-x64", "android.23", "android.22-x64", "android.22", "android.21-x64", "android.21", "android-x64", "android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "arch-x64": ["arch", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos-x64": ["centos", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos.7-x64": ["centos.7", "centos-x64", "rhel.7-x64", "centos", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos.8-x64": ["centos.8", "centos-x64", "rhel.8-x64", "centos", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "centos.9-x64": ["centos.9", "centos-x64", "rhel.9-x64", "centos", "rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian-x64": ["debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.10-x64": ["debian.10", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.11-x64": ["debian.11", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.12-x64": ["debian.12", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.8-x64": ["debian.8", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "debian.9-x64": ["debian.9", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "exherbo-x64": ["exherbo", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora-x64": ["fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.23-x64": ["fedora.23", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.24-x64": ["fedora.24", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.25-x64": ["fedora.25", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.26-x64": ["fedora.26", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.27-x64": ["fedora.27", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.28-x64": ["fedora.28", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.29-x64": ["fedora.29", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.30-x64": ["fedora.30", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.31-x64": ["fedora.31", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.32-x64": ["fedora.32", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.33-x64": ["fedora.33", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.34-x64": ["fedora.34", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.35-x64": ["fedora.35", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.36-x64": ["fedora.36", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.37-x64": ["fedora.37", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.38-x64": ["fedora.38", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "fedora.39-x64": ["fedora.39", "fedora-x64", "fedora", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "gentoo-x64": ["gentoo", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"], "linuxmint.17-x64": ["linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.17.1-x64": ["linuxmint.17.1", "linuxmint.17-x64", "linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.17.2-x64": ["linuxmint.17.2", "linuxmint.17.1-x64", "linuxmint.17.1", "linuxmint.17-x64", "linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.17.3-x64": ["linuxmint.17.3", "linuxmint.17.2-x64", "linuxmint.17.2", "linuxmint.17.1-x64", "linuxmint.17.1", "linuxmint.17-x64", "linuxmint.17", "ubuntu.14.04-x64", "ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18-x64": ["linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18.1-x64": ["linuxmint.18.1", "linuxmint.18-x64", "linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18.2-x64": ["linuxmint.18.2", "linuxmint.18.1-x64", "linuxmint.18.1", "linuxmint.18-x64", "linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.18.3-x64": ["linuxmint.18.3", "linuxmint.18.2-x64", "linuxmint.18.2", "linuxmint.18.1-x64", "linuxmint.18.1", "linuxmint.18-x64", "linuxmint.18", "ubuntu.16.04-x64", "ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.19-x64": ["linuxmint.19", "ubuntu.18.04-x64", "ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.19.1-x64": ["linuxmint.19.1", "linuxmint.19-x64", "linuxmint.19", "ubuntu.18.04-x64", "ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linuxmint.19.2-x64": ["linuxmint.19.2", "linuxmint.19.1-x64", "linuxmint.19.1", "linuxmint.19-x64", "linuxmint.19", "ubuntu.18.04-x64", "ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "manjaro-x64": ["manjaro", "arch-x64", "arch", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "miraclelinux-x64": ["miracle<PERSON><PERSON>", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "miraclelinux.8-x64": ["miraclelinux.8", "miraclelinux-x64", "rhel.8-x64", "miracle<PERSON><PERSON>", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "miraclelinux.9-x64": ["miraclelinux.9", "miraclelinux-x64", "rhel.9-x64", "miracle<PERSON><PERSON>", "rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol-x64": ["ol", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7-x64": ["ol.7", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.0-x64": ["ol.7.0", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.1-x64": ["ol.7.1", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.2-x64": ["ol.7.2", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.3-x64": ["ol.7.3", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.4-x64": ["ol.7.4", "ol.7.3-x64", "rhel.7.4-x64", "ol.7.3", "rhel.7.4", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.5-x64": ["ol.7.5", "ol.7.4-x64", "rhel.7.5-x64", "ol.7.4", "rhel.7.5", "ol.7.3-x64", "rhel.7.4-x64", "ol.7.3", "rhel.7.4", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.7.6-x64": ["ol.7.6", "ol.7.5-x64", "rhel.7.6-x64", "ol.7.5", "rhel.7.6", "ol.7.4-x64", "rhel.7.5-x64", "ol.7.4", "rhel.7.5", "ol.7.3-x64", "rhel.7.4-x64", "ol.7.3", "rhel.7.4", "ol.7.2-x64", "rhel.7.3-x64", "ol.7.2", "rhel.7.3", "ol.7.1-x64", "rhel.7.2-x64", "ol.7.1", "rhel.7.2", "ol.7.0-x64", "rhel.7.1-x64", "ol.7.0", "rhel.7.1", "ol.7-x64", "rhel.7.0-x64", "ol.7", "rhel.7.0", "ol-x64", "rhel.7-x64", "ol", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.8-x64": ["ol.8", "ol-x64", "rhel.8-x64", "ol", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ol.8.0-x64": ["ol.8.0", "ol.8-x64", "rhel.8.0-x64", "ol.8", "rhel.8.0", "ol-x64", "rhel.8-x64", "ol", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse-x64": ["opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.13.2-x64": ["opensuse.13.2", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.15.0-x64": ["opensuse.15.0", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.15.1-x64": ["opensuse.15.1", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.42.1-x64": ["opensuse.42.1", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.42.2-x64": ["opensuse.42.2", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "opensuse.42.3-x64": ["opensuse.42.3", "opensuse-x64", "opensuse", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel-x64": ["rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.6-x64": ["rhel.6", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7-x64": ["rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.0-x64": ["rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.1-x64": ["rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.2-x64": ["rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.3-x64": ["rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.4-x64": ["rhel.7.4", "rhel.7.3-x64", "rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.5-x64": ["rhel.7.5", "rhel.7.4-x64", "rhel.7.4", "rhel.7.3-x64", "rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.7.6-x64": ["rhel.7.6", "rhel.7.5-x64", "rhel.7.5", "rhel.7.4-x64", "rhel.7.4", "rhel.7.3-x64", "rhel.7.3", "rhel.7.2-x64", "rhel.7.2", "rhel.7.1-x64", "rhel.7.1", "rhel.7.0-x64", "rhel.7.0", "rhel.7-x64", "rhel.7", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.8-x64": ["rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.8.0-x64": ["rhel.8.0", "rhel.8-x64", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.8.1-x64": ["rhel.8.1", "rhel.8.0-x64", "rhel.8.0", "rhel.8-x64", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rhel.9-x64": ["rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rocky-x64": ["rocky", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rocky.8-x64": ["rocky.8", "rocky-x64", "rhel.8-x64", "rocky", "rhel.8", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "rocky.9-x64": ["rocky.9", "rocky-x64", "rhel.9-x64", "rocky", "rhel.9", "rhel-x64", "rhel", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles-x64": ["sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12-x64": ["sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.1-x64": ["sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.2-x64": ["sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.3-x64": ["sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.12.4-x64": ["sles.12.4", "sles.12.3-x64", "sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.15-x64": ["sles.15", "sles.12.4-x64", "sles.12.4", "sles.12.3-x64", "sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "sles.15.1-x64": ["sles.15.1", "sles.15-x64", "sles.15", "sles.12.4-x64", "sles.12.4", "sles.12.3-x64", "sles.12.3", "sles.12.2-x64", "sles.12.2", "sles.12.1-x64", "sles.12.1", "sles.12-x64", "sles.12", "sles-x64", "sles", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu-x64": ["ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.14.04-x64": ["ubuntu.14.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.14.10-x64": ["ubuntu.14.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.15.04-x64": ["ubuntu.15.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.15.10-x64": ["ubuntu.15.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.16.04-x64": ["ubuntu.16.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.16.10-x64": ["ubuntu.16.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.17.04-x64": ["ubuntu.17.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.17.10-x64": ["ubuntu.17.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.18.04-x64": ["ubuntu.18.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.18.10-x64": ["ubuntu.18.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.19.04-x64": ["ubuntu.19.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.19.10-x64": ["ubuntu.19.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.20.04-x64": ["ubuntu.20.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.20.10-x64": ["ubuntu.20.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.21.04-x64": ["ubuntu.21.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.21.10-x64": ["ubuntu.21.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.22.04-x64": ["ubuntu.22.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.22.10-x64": ["ubuntu.22.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.23.04-x64": ["ubuntu.23.04", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "ubuntu.23.10-x64": ["ubuntu.23.10", "ubuntu-x64", "ubuntu", "debian-x64", "debian", "linux-x64", "linux", "unix-x64", "unix", "any", "base"]}}